<script setup lang="ts">
import { TodoPage } from '../../../shared/pages/TodoPage'

interface Props {
  todoPage: TodoPage
}

const props = defineProps<Props>()

const handleKeyPress = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    props.todoPage.addTodo()
  }
}

const handleChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  props.todoPage.setNewTodoText(target.value)
}
</script>

<template>
  <div class="todo-input-container">
    <input
      type="text"
      class="todo-input"
      placeholder="What needs to be done?"
      :value="todoPage.getNewTodoText()"
      @input="handleChange"
      @keypress="handleKeyPress"
    />
    <button
      class="add-todo-btn"
      @click="todoPage.addTodo()"
      :disabled="!todoPage.getNewTodoText().trim()"
    >
      Add Todo
    </button>
  </div>
</template>

<style scoped>
.todo-input-container {
  flex: 1;
  display: flex;
  gap: 10px;
}

.todo-input {
  flex: 1;
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.todo-input:focus {
  outline: none;
  border-color: #3498db;
}

.add-todo-btn {
  padding: 12px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.add-todo-btn:hover:not(:disabled) {
  background: #2980b9;
}

.add-todo-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}
</style>
