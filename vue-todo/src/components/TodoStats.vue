<script setup lang="ts">
import { TodoPage } from '../../../shared/pages/TodoPage'

interface Props {
  todoPage: TodoPage
}

const props = defineProps<Props>()
</script>

<template>
  <div class="todo-stats">
    <div class="stats-section">
      <h3>Todo Statistics</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">Total:</span>
          <span class="stat-value">{{ todoPage.getStats().total }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Active:</span>
          <span class="stat-value">{{ todoPage.getStats().active }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Completed:</span>
          <span class="stat-value">{{ todoPage.getStats().completed }}</span>
        </div>
      </div>
    </div>

    <div class="stats-section">
      <h3>Event Sourcing Stats</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">Events:</span>
          <span class="stat-value">{{ todoPage.getEventStoreStats().totalEvents }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Version:</span>
          <span class="stat-value">{{ todoPage.getStats().version }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Event Types:</span>
          <span class="stat-value">{{ todoPage.getEventStoreStats().eventTypes.length }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.todo-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.stats-section {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stats-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.stat-label {
  font-weight: 500;
  color: #7f8c8d;
}

.stat-value {
  font-weight: bold;
  color: #2c3e50;
}

@media (max-width: 768px) {
  .todo-stats {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
