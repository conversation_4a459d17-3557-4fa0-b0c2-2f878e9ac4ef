<script setup lang="ts">
import { TodoPage } from '../../../shared/pages/TodoPage'

interface Props {
  todoPage: TodoPage
}

const props = defineProps<Props>()
</script>

<template>
  <div class="undo-redo-controls">
    <button
      class="undo-btn"
      @click="todoPage.undo()"
      :disabled="!todoPage.canUndo()"
      title="Undo last action"
    >
      ↶ Undo
    </button>
    <button
      class="redo-btn"
      @click="todoPage.redo()"
      :disabled="!todoPage.canRedo()"
      title="Redo last action"
    >
      ↷ Redo
    </button>
  </div>
</template>

<style scoped>
.undo-redo-controls {
  display: flex;
  gap: 8px;
}

.undo-btn, .redo-btn {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.undo-btn:hover:not(:disabled), .redo-btn:hover:not(:disabled) {
  border-color: #3498db;
  background: #f8f9fa;
}

.undo-btn:disabled, .redo-btn:disabled {
  color: #bdc3c7;
  cursor: not-allowed;
}
</style>
