<script setup lang="ts">
import { TodoPage, TodoFilter } from '../../../shared/pages/TodoPage'

interface Props {
  todoPage: TodoPage
}

const props = defineProps<Props>()

const filters: { key: TodoFilter; label: string }[] = [
  { key: 'all', label: 'All' },
  { key: 'active', label: 'Active' },
  { key: 'completed', label: 'Completed' },
]
</script>

<template>
  <div class="todo-filters">
    <div class="filter-buttons">
      <button
        v-for="filter in filters"
        :key="filter.key"
        :class="['filter-btn', { active: todoPage.getCurrentFilter() === filter.key }]"
        @click="todoPage.setFilter(filter.key)"
      >
        {{ filter.label }} ({{ 
          filter.key === 'all' ? todoPage.getStats().total :
          filter.key === 'active' ? todoPage.getStats().active :
          todoPage.getStats().completed 
        }})
      </button>
    </div>

    <button
      v-if="todoPage.hasCompletedTodos()"
      class="clear-completed-btn"
      @click="todoPage.clearCompleted()"
    >
      Clear Completed
    </button>
  </div>
</template>

<style scoped>
.todo-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.filter-buttons {
  display: flex;
  gap: 10px;
}

.filter-btn {
  padding: 8px 16px;
  border: 2px solid #e1e8ed;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.filter-btn:hover {
  border-color: #3498db;
}

.filter-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.clear-completed-btn {
  padding: 8px 16px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.clear-completed-btn:hover {
  background: #c0392b;
}

@media (max-width: 768px) {
  .todo-filters {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
