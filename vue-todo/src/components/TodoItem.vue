<script setup lang="ts">
import { ref } from 'vue'
import { Todo } from '../../../shared/core/entities/Todo'
import { TodoPage } from '../../../shared/pages/TodoPage'

interface Props {
  todo: Todo
  todoPage: TodoPage
}

const props = defineProps<Props>()

const isEditing = ref(false)
const editText = ref(props.todo.text)

const handleToggle = () => {
  props.todoPage.toggleTodo(props.todo.id)
}

const handleDelete = () => {
  props.todoPage.removeTodo(props.todo.id)
}

const handleEdit = () => {
  isEditing.value = true
  editText.value = props.todo.text
}

const handleSave = () => {
  if (editText.value.trim()) {
    props.todoPage.updateTodoText(props.todo.id, editText.value.trim())
  }
  isEditing.value = false
}

const handleCancel = () => {
  editText.value = props.todo.text
  isEditing.value = false
}

const handleKeyPress = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    handleSave()
  } else if (e.key === 'Escape') {
    handleCancel()
  }
}
</script>

<template>
  <div :class="['todo-item', { completed: todo.completed }]">
    <div class="todo-item-content">
      <input
        type="checkbox"
        class="todo-checkbox"
        :checked="todo.completed"
        @change="handleToggle"
      />
      
      <input
        v-if="isEditing"
        type="text"
        class="todo-edit-input"
        v-model="editText"
        @keypress="handleKeyPress"
        @blur="handleSave"
        ref="editInput"
      />
      
      <span 
        v-else
        class="todo-text"
        @dblclick="handleEdit"
      >
        {{ todo.text }}
      </span>
    </div>

    <div class="todo-item-actions">
      <template v-if="!isEditing">
        <button
          class="edit-btn"
          @click="handleEdit"
          title="Edit todo"
        >
          ✏️
        </button>
        <button
          class="delete-btn"
          @click="handleDelete"
          title="Delete todo"
        >
          🗑️
        </button>
      </template>
      
      <template v-else>
        <button
          class="save-btn"
          @click="handleSave"
          title="Save changes"
        >
          ✅
        </button>
        <button
          class="cancel-btn"
          @click="handleCancel"
          title="Cancel editing"
        >
          ❌
        </button>
      </template>
    </div>
  </div>
</template>

<style scoped>
.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e1e8ed;
  transition: background-color 0.2s;
}

.todo-item:hover {
  background: #f8f9fa;
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 12px;
}

.todo-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.todo-text {
  flex: 1;
  font-size: 16px;
  cursor: pointer;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
}

.todo-edit-input {
  flex: 1;
  padding: 8px;
  border: 2px solid #3498db;
  border-radius: 4px;
  font-size: 16px;
}

.todo-item-actions {
  display: flex;
  gap: 8px;
}

.edit-btn, .delete-btn, .save-btn, .cancel-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: opacity 0.2s;
}

.edit-btn, .save-btn {
  background: #f39c12;
}

.delete-btn, .cancel-btn {
  background: #e74c3c;
}

.edit-btn:hover, .delete-btn:hover, .save-btn:hover, .cancel-btn:hover {
  opacity: 0.8;
}
</style>
