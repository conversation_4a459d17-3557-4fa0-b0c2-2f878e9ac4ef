<script setup lang="ts">
import { usePageInstance } from '../../../shared/adapters/vue'
import { TodoPage } from '../../../shared/pages/TodoPage'
import TodoInput from './TodoInput.vue'
import TodoList from './TodoList.vue'
import TodoFilters from './TodoFilters.vue'
import TodoStats from './TodoStats.vue'
import UndoRedoControls from './UndoRedoControls.vue'

/**
 * Vue Todo Application demonstrating framework-agnostic architecture
 * Uses the shared TodoPage class through the Vue adapter
 */
const todoPage = usePageInstance(TodoPage)
</script>

<template>
  <div class="todo-app">
    <header class="todo-header">
      <h1>Vue Todo App</h1>
      <p class="architecture-note">
        Framework-Agnostic Reactive Architecture Demo
      </p>
    </header>

    <main class="todo-main">
      <div class="todo-controls">
        <TodoInput :todo-page="todoPage" />
        <UndoRedoControls :todo-page="todoPage" />
      </div>

      <TodoFilters :todo-page="todoPage" />
      <TodoList :todo-page="todoPage" />
      <TodoStats :todo-page="todoPage" />
    </main>

    <footer class="todo-footer">
      <p>
        This app demonstrates a framework-agnostic reactive architecture.
        The same business logic powers both React and Vue versions.
      </p>
    </footer>
  </div>
</template>

<style scoped>
.todo-app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.todo-header {
  text-align: center;
  margin-bottom: 30px;
}

.todo-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.architecture-note {
  color: #7f8c8d;
  font-style: italic;
  margin: 0;
}

.todo-main {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.todo-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.todo-footer {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .todo-app {
    padding: 10px;
  }
  
  .todo-controls {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
