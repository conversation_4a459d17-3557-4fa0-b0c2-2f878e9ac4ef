<script setup lang="ts">
import { TodoPage } from '../../../shared/pages/TodoPage'
import TodoItem from './TodoItem.vue'

interface Props {
  todoPage: TodoPage
}

const props = defineProps<Props>()

const getEmptyMessage = () => {
  const filter = props.todoPage.getCurrentFilter()
  return filter === 'all' 
    ? 'No todos yet. Add one above!'
    : filter === 'active'
    ? 'No active todos!'
    : 'No completed todos!'
}
</script>

<template>
  <div class="todo-list">
    <div v-if="todoPage.getFilteredTodos().length === 0" class="todo-list-empty">
      <p>{{ getEmptyMessage() }}</p>
    </div>
    
    <TodoItem
      v-else
      v-for="todo in todoPage.getFilteredTodos()"
      :key="todo.id.value"
      :todo="todo"
      :todo-page="todoPage"
    />
  </div>
</template>

<style scoped>
.todo-list {
  margin-bottom: 20px;
}

.todo-list-empty {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
  font-style: italic;
}
</style>
