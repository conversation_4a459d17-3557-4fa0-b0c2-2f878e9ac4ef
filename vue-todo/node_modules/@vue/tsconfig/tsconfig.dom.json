{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "lib": [
      // Target ES2020 to align with Vite.
      // <https://vitejs.dev/config/build-options.html#build-target>
      // Support for newer versions of language built-ins are
      // left for the users to include, because that would require:
      //   - either the project doesn't need to support older versions of browsers;
      //   - or the project has properly included the necessary polyfills.
      "ES2020",

      "DOM",
      "DOM.Iterable"

      // No `ScriptHost` because Vue 3 dropped support for IE
    ],

    // Set to empty to avoid accidental inclusion of unwanted types
    "types": []
  }
}
