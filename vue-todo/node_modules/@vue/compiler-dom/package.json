{"name": "@vue/compiler-dom", "version": "3.5.16", "description": "@vue/compiler-dom", "main": "index.js", "module": "dist/compiler-dom.esm-bundler.js", "types": "dist/compiler-dom.d.ts", "unpkg": "dist/compiler-dom.global.js", "jsdelivr": "dist/compiler-dom.global.js", "files": ["index.js", "dist"], "exports": {".": {"types": "./dist/compiler-dom.d.ts", "node": {"production": "./dist/compiler-dom.cjs.prod.js", "development": "./dist/compiler-dom.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-dom.esm-bundler.js", "import": "./dist/compiler-dom.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "sideEffects": false, "buildOptions": {"name": "VueCompilerDOM", "compat": true, "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-dom"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-dom#readme", "dependencies": {"@vue/shared": "3.5.16", "@vue/compiler-core": "3.5.16"}}