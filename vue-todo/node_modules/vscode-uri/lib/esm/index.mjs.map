{"version": 3, "file": "index.mjs", "mappings": "0CA0BA,SAASA,EAAWC,GAClB,GAAoB,iBAATA,EACT,MAAM,IAAIC,UAAU,mCAAqCC,KAAKC,UAAUH,GAE5E,CAGA,SAASI,EAAqBJ,EAAMK,GAMlC,IALA,IAIIC,EAJAC,EAAM,GACNC,EAAoB,EACpBC,GAAa,EACbC,EAAO,EAEFC,EAAI,EAAGA,GAAKX,EAAKY,SAAUD,EAAG,CACrC,GAAIA,EAAIX,EAAKY,OACXN,EAAON,EAAKa,WAAWF,OACpB,IAAa,KAATL,EACP,MAEAA,EAAO,EAAQ,CACjB,GAAa,KAATA,EAAmB,CACrB,GAAIG,IAAcE,EAAI,GAAc,IAATD,QAEpB,GAAID,IAAcE,EAAI,GAAc,IAATD,EAAY,CAC5C,GAAIH,EAAIK,OAAS,GAA2B,IAAtBJ,GAA8D,KAAnCD,EAAIM,WAAWN,EAAIK,OAAS,IAAsD,KAAnCL,EAAIM,WAAWN,EAAIK,OAAS,GAC1H,GAAIL,EAAIK,OAAS,EAAG,CAClB,IAAIE,EAAiBP,EAAIQ,YAAY,KACrC,GAAID,IAAmBP,EAAIK,OAAS,EAAG,EACb,IAApBE,GACFP,EAAM,GACNC,EAAoB,GAGpBA,GADAD,EAAMA,EAAIS,MAAM,EAAGF,IACKF,OAAS,EAAIL,EAAIQ,YAAY,KAEvDN,EAAYE,EACZD,EAAO,EACP,QACF,CACF,MAAO,GAAmB,IAAfH,EAAIK,QAA+B,IAAfL,EAAIK,OAAc,CAC/CL,EAAM,GACNC,EAAoB,EACpBC,EAAYE,EACZD,EAAO,EACP,QACF,CAEEL,IACEE,EAAIK,OAAS,EACfL,GAAO,MAEPA,EAAM,KACRC,EAAoB,EAExB,MACMD,EAAIK,OAAS,EACfL,GAAO,IAAMP,EAAKgB,MAAMP,EAAY,EAAGE,GAEvCJ,EAAMP,EAAKgB,MAAMP,EAAY,EAAGE,GAClCH,EAAoBG,EAAIF,EAAY,EAEtCA,EAAYE,EACZD,EAAO,CACT,MAAoB,KAATJ,IAA+B,IAAVI,IAC5BA,EAEFA,GAAQ,CAEZ,CACA,OAAOH,CACT,CAcA,IAAIU,EAAQ,CAEVC,QAAS,WAKP,IAJA,IAEIC,EAFAC,EAAe,GACfC,GAAmB,EAGdV,EAAIW,UAAUV,OAAS,EAAGD,IAAM,IAAMU,EAAkBV,IAAK,CACpE,IAAIX,EACAW,GAAK,EACPX,EAAOsB,UAAUX,SAELY,IAARJ,IACFA,EAAMK,QAAQL,OAChBnB,EAAOmB,GAGTpB,EAAWC,GAGS,IAAhBA,EAAKY,SAITQ,EAAepB,EAAO,IAAMoB,EAC5BC,EAA0C,KAAvBrB,EAAKa,WAAW,GACrC,CAQA,OAFAO,EAAehB,EAAqBgB,GAAeC,GAE/CA,EACED,EAAaR,OAAS,EACjB,IAAMQ,EAEN,IACAA,EAAaR,OAAS,EACxBQ,EAEA,GAEX,EAEAK,UAAW,SAAmBzB,GAG5B,GAFAD,EAAWC,GAES,IAAhBA,EAAKY,OAAc,MAAO,IAE9B,IAAIc,EAAoC,KAAvB1B,EAAKa,WAAW,GAC7Bc,EAAyD,KAArC3B,EAAKa,WAAWb,EAAKY,OAAS,GAQtD,OAHoB,KAFpBZ,EAAOI,EAAqBJ,GAAO0B,IAE1Bd,QAAiBc,IAAY1B,EAAO,KACzCA,EAAKY,OAAS,GAAKe,IAAmB3B,GAAQ,KAE9C0B,EAAmB,IAAM1B,EACtBA,CACT,EAEA0B,WAAY,SAAoB1B,GAE9B,OADAD,EAAWC,GACJA,EAAKY,OAAS,GAA4B,KAAvBZ,EAAKa,WAAW,EAC5C,EAEAe,KAAM,WACJ,GAAyB,IAArBN,UAAUV,OACZ,MAAO,IAET,IADA,IAAIiB,EACKlB,EAAI,EAAGA,EAAIW,UAAUV,SAAUD,EAAG,CACzC,IAAImB,EAAMR,UAAUX,GACpBZ,EAAW+B,GACPA,EAAIlB,OAAS,SACAW,IAAXM,EACFA,EAASC,EAETD,GAAU,IAAMC,EAEtB,CACA,YAAeP,IAAXM,EACK,IACFZ,EAAMQ,UAAUI,EACzB,EAEAE,SAAU,SAAkBC,EAAMC,GAIhC,GAHAlC,EAAWiC,GACXjC,EAAWkC,GAEPD,IAASC,EAAI,MAAO,GAKxB,IAHAD,EAAOf,EAAMC,QAAQc,OACrBC,EAAKhB,EAAMC,QAAQe,IAEF,MAAO,GAIxB,IADA,IAAIC,EAAY,EACTA,EAAYF,EAAKpB,QACa,KAA/BoB,EAAKnB,WAAWqB,KADYA,GASlC,IALA,IAAIC,EAAUH,EAAKpB,OACfwB,EAAUD,EAAUD,EAGpBG,EAAU,EACPA,EAAUJ,EAAGrB,QACa,KAA3BqB,EAAGpB,WAAWwB,KADUA,GAW9B,IAPA,IACIC,EADQL,EAAGrB,OACKyB,EAGhBzB,EAASwB,EAAUE,EAAQF,EAAUE,EACrCC,GAAiB,EACjB5B,EAAI,EACDA,GAAKC,IAAUD,EAAG,CACvB,GAAIA,IAAMC,EAAQ,CAChB,GAAI0B,EAAQ1B,EAAQ,CAClB,GAAmC,KAA/BqB,EAAGpB,WAAWwB,EAAU1B,GAG1B,OAAOsB,EAAGjB,MAAMqB,EAAU1B,EAAI,GACzB,GAAU,IAANA,EAGT,OAAOsB,EAAGjB,MAAMqB,EAAU1B,EAE9B,MAAWyB,EAAUxB,IACoB,KAAnCoB,EAAKnB,WAAWqB,EAAYvB,GAG9B4B,EAAgB5B,EACD,IAANA,IAGT4B,EAAgB,IAGpB,KACF,CACA,IAAIC,EAAWR,EAAKnB,WAAWqB,EAAYvB,GAE3C,GAAI6B,IADSP,EAAGpB,WAAWwB,EAAU1B,GAEnC,MACoB,KAAb6B,IACPD,EAAgB5B,EACpB,CAEA,IAAI8B,EAAM,GAGV,IAAK9B,EAAIuB,EAAYK,EAAgB,EAAG5B,GAAKwB,IAAWxB,EAClDA,IAAMwB,GAAkC,KAAvBH,EAAKnB,WAAWF,KAChB,IAAf8B,EAAI7B,OACN6B,GAAO,KAEPA,GAAO,OAMb,OAAIA,EAAI7B,OAAS,EACR6B,EAAMR,EAAGjB,MAAMqB,EAAUE,IAEhCF,GAAWE,EACoB,KAA3BN,EAAGpB,WAAWwB,MACdA,EACGJ,EAAGjB,MAAMqB,GAEpB,EAEAK,UAAW,SAAmB1C,GAC5B,OAAOA,CACT,EAEA2C,QAAS,SAAiB3C,GAExB,GADAD,EAAWC,GACS,IAAhBA,EAAKY,OAAc,MAAO,IAK9B,IAJA,IAAIN,EAAON,EAAKa,WAAW,GACvB+B,EAAmB,KAATtC,EACVuC,GAAO,EACPC,GAAe,EACVnC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAEtC,GAAa,MADbL,EAAON,EAAKa,WAAWF,KAEnB,IAAKmC,EAAc,CACjBD,EAAMlC,EACN,KACF,OAGFmC,GAAe,EAInB,OAAa,IAATD,EAAmBD,EAAU,IAAM,IACnCA,GAAmB,IAARC,EAAkB,KAC1B7C,EAAKgB,MAAM,EAAG6B,EACvB,EAEAE,SAAU,SAAkB/C,EAAMgD,GAChC,QAAYzB,IAARyB,GAAoC,iBAARA,EAAkB,MAAM,IAAI/C,UAAU,mCACtEF,EAAWC,GAEX,IAGIW,EAHAsC,EAAQ,EACRJ,GAAO,EACPC,GAAe,EAGnB,QAAYvB,IAARyB,GAAqBA,EAAIpC,OAAS,GAAKoC,EAAIpC,QAAUZ,EAAKY,OAAQ,CACpE,GAAIoC,EAAIpC,SAAWZ,EAAKY,QAAUoC,IAAQhD,EAAM,MAAO,GACvD,IAAIkD,EAASF,EAAIpC,OAAS,EACtBuC,GAAoB,EACxB,IAAKxC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACrC,IAAIL,EAAON,EAAKa,WAAWF,GAC3B,GAAa,KAATL,GAGA,IAAKwC,EAAc,CACjBG,EAAQtC,EAAI,EACZ,KACF,OAEwB,IAAtBwC,IAGFL,GAAe,EACfK,EAAmBxC,EAAI,GAErBuC,GAAU,IAER5C,IAAS0C,EAAInC,WAAWqC,IACR,KAAZA,IAGJL,EAAMlC,IAKRuC,GAAU,EACVL,EAAMM,GAId,CAGA,OADIF,IAAUJ,EAAKA,EAAMM,GAAmC,IAATN,IAAYA,EAAM7C,EAAKY,QACnEZ,EAAKgB,MAAMiC,EAAOJ,EAC3B,CACE,IAAKlC,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAClC,GAA2B,KAAvBX,EAAKa,WAAWF,IAGhB,IAAKmC,EAAc,CACjBG,EAAQtC,EAAI,EACZ,KACF,OACkB,IAATkC,IAGXC,GAAe,EACfD,EAAMlC,EAAI,GAId,OAAa,IAATkC,EAAmB,GAChB7C,EAAKgB,MAAMiC,EAAOJ,EAE7B,EAEAO,QAAS,SAAiBpD,GACxBD,EAAWC,GAQX,IAPA,IAAIqD,GAAY,EACZC,EAAY,EACZT,GAAO,EACPC,GAAe,EAGfS,EAAc,EACT5C,EAAIX,EAAKY,OAAS,EAAGD,GAAK,IAAKA,EAAG,CACzC,IAAIL,EAAON,EAAKa,WAAWF,GAC3B,GAAa,KAATL,GASS,IAATuC,IAGFC,GAAe,EACfD,EAAMlC,EAAI,GAEC,KAATL,GAEkB,IAAd+C,EACFA,EAAW1C,EACY,IAAhB4C,IACPA,EAAc,IACK,IAAdF,IAGTE,GAAe,QArBb,IAAKT,EAAc,CACjBQ,EAAY3C,EAAI,EAChB,KACF,CAoBN,CAEA,OAAkB,IAAd0C,IAA4B,IAATR,GAEH,IAAhBU,GAEgB,IAAhBA,GAAqBF,IAAaR,EAAM,GAAKQ,IAAaC,EAAY,EACjE,GAEFtD,EAAKgB,MAAMqC,EAAUR,EAC9B,EAEAW,OAAQ,SAAgBC,GACtB,GAAmB,OAAfA,GAA6C,iBAAfA,EAChC,MAAM,IAAIxD,UAAU,0EAA4EwD,GAElG,OAvVJ,SAAiBC,EAAKD,GACpB,IAAIE,EAAMF,EAAWE,KAAOF,EAAWG,KACnCC,EAAOJ,EAAWI,OAASJ,EAAWK,MAAQ,KAAOL,EAAWT,KAAO,IAC3E,OAAKW,EAGDA,IAAQF,EAAWG,KACdD,EAAME,EAERF,EA8UU,IA9UEE,EALVA,CAMX,CA6UWE,CAAQ,EAAKN,EACtB,EAEAO,MAAO,SAAehE,GACpBD,EAAWC,GAEX,IAAIiE,EAAM,CAAEL,KAAM,GAAID,IAAK,GAAIE,KAAM,GAAIb,IAAK,GAAIc,KAAM,IACxD,GAAoB,IAAhB9D,EAAKY,OAAc,OAAOqD,EAC9B,IAEIhB,EAFA3C,EAAON,EAAKa,WAAW,GACvBa,EAAsB,KAATpB,EAEboB,GACFuC,EAAIL,KAAO,IACXX,EAAQ,GAERA,EAAQ,EAaV,IAXA,IAAII,GAAY,EACZC,EAAY,EACZT,GAAO,EACPC,GAAe,EACfnC,EAAIX,EAAKY,OAAS,EAIlB2C,EAAc,EAGX5C,GAAKsC,IAAStC,EAEnB,GAAa,MADbL,EAAON,EAAKa,WAAWF,KAUV,IAATkC,IAGFC,GAAe,EACfD,EAAMlC,EAAI,GAEC,KAATL,GAEkB,IAAd+C,EAAiBA,EAAW1C,EAA2B,IAAhB4C,IAAmBA,EAAc,IACrD,IAAdF,IAGXE,GAAe,QAlBb,IAAKT,EAAc,CACjBQ,EAAY3C,EAAI,EAChB,KACF,CAwCN,OArBkB,IAAd0C,IAA4B,IAATR,GAEP,IAAhBU,GAEgB,IAAhBA,GAAqBF,IAAaR,EAAM,GAAKQ,IAAaC,EAAY,GACvD,IAATT,IACiCoB,EAAIJ,KAAOI,EAAIH,KAAhC,IAAdR,GAAmB5B,EAAkC1B,EAAKgB,MAAM,EAAG6B,GAAgC7C,EAAKgB,MAAMsC,EAAWT,KAG7G,IAAdS,GAAmB5B,GACrBuC,EAAIH,KAAO9D,EAAKgB,MAAM,EAAGqC,GACzBY,EAAIJ,KAAO7D,EAAKgB,MAAM,EAAG6B,KAEzBoB,EAAIH,KAAO9D,EAAKgB,MAAMsC,EAAWD,GACjCY,EAAIJ,KAAO7D,EAAKgB,MAAMsC,EAAWT,IAEnCoB,EAAIjB,IAAMhD,EAAKgB,MAAMqC,EAAUR,IAG7BS,EAAY,EAAGW,EAAIN,IAAM3D,EAAKgB,MAAM,EAAGsC,EAAY,GAAY5B,IAAYuC,EAAIN,IAAM,KAElFM,CACT,EAEAP,IAAK,IACLQ,UAAW,IACXC,MAAO,KACPlD,MAAO,MAGTA,EAAMA,MAAQA,EAEdmD,EAAOC,QAAUpD,C,GC/gBbqD,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBjD,IAAjBkD,EACH,OAAOA,EAAaJ,QAGrB,IAAID,EAASE,EAAyBE,GAAY,CAGjDH,QAAS,CAAC,GAOX,OAHAK,EAAoBF,GAAUJ,EAAQA,EAAOC,QAASE,GAG/CH,EAAOC,OACf,CCrBAE,EAAoBI,EAAI,CAACN,EAASO,KACjC,IAAI,IAAIC,KAAOD,EACXL,EAAoBO,EAAEF,EAAYC,KAASN,EAAoBO,EAAET,EAASQ,IAC5EE,OAAOC,eAAeX,EAASQ,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDN,EAAoBO,EAAI,CAACK,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFb,EAAoBiB,EAAKnB,IACH,oBAAXoB,QAA0BA,OAAOC,aAC1CX,OAAOC,eAAeX,EAASoB,OAAOC,YAAa,CAAEC,MAAO,WAE7DZ,OAAOC,eAAeX,EAAS,aAAc,CAAEsB,OAAO,GAAO,E,SCQvD,IAAIC,EAEX,G,sCAAuB,iBAAZpE,QACVoE,EAAiC,UAArBpE,QAAQqE,cACd,GAAyB,iBAAdC,UAAwB,CACzC,IAAIC,EAAYD,UAAUC,UAC1BH,EAAYG,EAAUC,QAAQ,YAAc,C,CCV7C,MAAMC,EAAiB,iBACjBC,EAAoB,MACpBC,EAAoB,QAE1B,SAASC,EAAanC,EAAUoC,GAG/B,IAAKpC,EAAIqC,QAAUD,EAClB,MAAM,IAAIE,MAAM,2DAA2DtC,EAAIuC,sBAAsBvC,EAAIjE,kBAAkBiE,EAAIwC,sBAAsBxC,EAAIyC,cAK1J,GAAIzC,EAAIqC,SAAWL,EAAeU,KAAK1C,EAAIqC,QAC1C,MAAM,IAAIC,MAAM,mDAQjB,GAAItC,EAAIjE,KACP,GAAIiE,EAAIuC,WACP,IAAKN,EAAkBS,KAAK1C,EAAIjE,MAC/B,MAAM,IAAIuG,MAAM,iJAGjB,GAAIJ,EAAkBQ,KAAK1C,EAAIjE,MAC9B,MAAM,IAAIuG,MAAM,4HAIpB,CAkCA,MAAMK,EAAS,GACTC,EAAS,IACTC,EAAU,+DAkBT,MAAMC,EAEZ,YAAOC,CAAMC,GACZ,OAAIA,aAAiBF,KAGhBE,GAGoC,iBAArBA,EAAOT,WACU,iBAApBS,EAAOP,UACS,iBAAhBO,EAAOjH,MACU,iBAAjBiH,EAAOR,OACW,iBAAlBQ,EAAOX,QACW,iBAAlBW,EAAOC,QACS,mBAAhBD,EAAOE,MACa,mBAApBF,EAAOG,QACzB,CAMSd,OAMAE,UAKAxG,KAKAyG,MAKAC,SAeT,YAAsBW,EAAsCb,EAAoBxG,EAAeyG,EAAgBC,EAAmBL,GAAmB,GAExH,iBAAjBgB,GACVC,KAAKhB,OAASe,EAAaf,QAAUM,EACrCU,KAAKd,UAAYa,EAAab,WAAaI,EAC3CU,KAAKtH,KAAOqH,EAAarH,MAAQ4G,EACjCU,KAAKb,MAAQY,EAAaZ,OAASG,EACnCU,KAAKZ,SAAWW,EAAaX,UAAYE,IAKzCU,KAAKhB,OAvHR,SAAoBA,EAAgBD,GACnC,OAAKC,GAAWD,EAGTC,EAFC,MAGT,CAkHiBiB,CAAWF,EAAchB,GACvCiB,KAAKd,UAAYA,GAAaI,EAC9BU,KAAKtH,KAjHR,SAA8BsG,EAAgBtG,GAM7C,OAAQsG,GACP,IAAK,QACL,IAAK,OACL,IAAK,OACCtG,EAEMA,EAAK,KAAO6G,IACtB7G,EAAO6G,EAAS7G,GAFhBA,EAAO6G,EAMV,OAAO7G,CACR,CA+FewH,CAAqBF,KAAKhB,OAAQtG,GAAQ4G,GACtDU,KAAKb,MAAQA,GAASG,EACtBU,KAAKZ,SAAWA,GAAYE,EAE5BR,EAAakB,KAAMjB,GAErB,CA4BA,UAAIa,GAIH,OAAOO,EAAYH,MAAM,EAC1B,CAIA,KAAKI,GAEJ,IAAKA,EACJ,OAAOJ,KAGR,IAAI,OAAEhB,EAAM,UAAEE,EAAS,KAAExG,EAAI,MAAEyG,EAAK,SAAEC,GAAagB,EA2BnD,YA1BenG,IAAX+E,EACHA,EAASgB,KAAKhB,OACO,OAAXA,IACVA,EAASM,QAEQrF,IAAdiF,EACHA,EAAYc,KAAKd,UACO,OAAdA,IACVA,EAAYI,QAEArF,IAATvB,EACHA,EAAOsH,KAAKtH,KACO,OAATA,IACVA,EAAO4G,QAEMrF,IAAVkF,EACHA,EAAQa,KAAKb,MACO,OAAVA,IACVA,EAAQG,QAEQrF,IAAbmF,EACHA,EAAWY,KAAKZ,SACO,OAAbA,IACVA,EAAWE,GAGRN,IAAWgB,KAAKhB,QAChBE,IAAcc,KAAKd,WACnBxG,IAASsH,KAAKtH,MACdyG,IAAUa,KAAKb,OACfC,IAAaY,KAAKZ,SAEdY,KAGD,IAAIK,EAAIrB,EAAQE,EAAWxG,EAAMyG,EAAOC,EAChD,CAUA,YAAO1C,CAAM2B,EAAeU,GAAmB,GAC9C,MAAMuB,EAAQd,EAAQe,KAAKlC,GAC3B,OAAKiC,EAGE,IAAID,EACVC,EAAM,IAAMhB,EACZkB,EAAcF,EAAM,IAAMhB,GAC1BkB,EAAcF,EAAM,IAAMhB,GAC1BkB,EAAcF,EAAM,IAAMhB,GAC1BkB,EAAcF,EAAM,IAAMhB,GAC1BP,GARO,IAAIsB,EAAIf,EAAQA,EAAQA,EAAQA,EAAQA,EAUjD,CAuBA,WAAOmB,CAAK/H,GAEX,IAAIwG,EAAYI,EAWhB,GANIhB,IACH5F,EAAOA,EAAKgI,QAAQ,MAAOnB,IAKxB7G,EAAK,KAAO6G,GAAU7G,EAAK,KAAO6G,EAAQ,CAC7C,MAAMoB,EAAMjI,EAAKgG,QAAQa,EAAQ,IACpB,IAAToB,GACHzB,EAAYxG,EAAKkI,UAAU,GAC3BlI,EAAO6G,IAEPL,EAAYxG,EAAKkI,UAAU,EAAGD,GAC9BjI,EAAOA,EAAKkI,UAAUD,IAAQpB,E,CAIhC,OAAO,IAAIc,EAAI,OAAQnB,EAAWxG,EAAM4G,EAAQA,EACjD,CAEA,WAAO5E,CAAKmG,GACX,MAAMC,EAAS,IAAIT,EAClBQ,EAAW7B,OACX6B,EAAW3B,UACX2B,EAAWnI,KACXmI,EAAW1B,MACX0B,EAAWzB,UAGZ,OADAN,EAAagC,GAAQ,GACdA,CACR,CAeA,QAAAhB,CAASiB,GAAwB,GAChC,OAAOC,EAAahB,KAAMe,EAC3B,CAEA,MAAAE,GACC,OAAOjB,IACR,CAMA,aAAOkB,CAAOC,GACb,GAAKA,EAEE,IAAIA,aAAgB1B,EAC1B,OAAO0B,EACD,CACN,MAAML,EAAS,IAAIT,EAAIc,GAGvB,OAFAL,EAAOM,WAAwBD,EAAME,SACrCP,EAAOQ,QAAqBH,EAAMI,OAASC,EAA4BL,EAAMvB,OAAS,KAC/EkB,C,EAPP,OAAYK,CASd,EAkBD,MAAMK,EAAiBlD,EAAY,OAAIrE,EAGvC,MAAMoG,UAAYZ,EAEjB2B,WAA4B,KAC5BE,QAAyB,KAEzB,UAAa1B,GAIZ,OAHKI,KAAKsB,UACTtB,KAAKsB,QAAUnB,EAAYH,MAAM,IAE3BA,KAAKsB,OACb,CAES,QAAAxB,CAASiB,GAAwB,GACzC,OAAKA,EAOGC,EAAahB,MAAM,IANrBA,KAAKoB,aACTpB,KAAKoB,WAAaJ,EAAahB,MAAM,IAE/BA,KAAKoB,WAKd,CAES,MAAAH,GACR,MAAMhI,EAAgB,CACrBwI,KAAM,GA0BP,OAvBIzB,KAAKsB,UACRrI,EAAI2G,OAASI,KAAKsB,QAClBrI,EAAIsI,KAAOC,GAERxB,KAAKoB,aACRnI,EAAIoI,SAAWrB,KAAKoB,YAGjBpB,KAAKtH,OACRO,EAAIP,KAAOsH,KAAKtH,MAEbsH,KAAKhB,SACR/F,EAAI+F,OAASgB,KAAKhB,QAEfgB,KAAKd,YACRjG,EAAIiG,UAAYc,KAAKd,WAElBc,KAAKb,QACRlG,EAAIkG,MAAQa,KAAKb,OAEda,KAAKZ,WACRnG,EAAImG,SAAWY,KAAKZ,UAEdnG,CACR,EAID,MAAMyI,EAAwC,CAC7C,GAAkB,MAClB,GAAkB,MAClB,GAAyB,MACzB,GAAiB,MACjB,GAA8B,MAC9B,GAA+B,MAC/B,GAAmB,MAEnB,GAA4B,MAC5B,GAAuB,MACvB,GAAsB,MACtB,GAAwB,MACxB,GAAsB,MACtB,GAAuB,MACvB,GAAqB,MACrB,GAAiB,MACjB,GAAkB,MAClB,GAAsB,MACtB,GAAmB,MAEnB,GAAkB,OAGnB,SAASC,EAAuBC,EAAsBC,EAAiBC,GACtE,IAAI7I,EACA8I,GAAmB,EAEvB,IAAK,IAAIC,EAAM,EAAGA,EAAMJ,EAAatI,OAAQ0I,IAAO,CACnD,MAAMhJ,EAAO4I,EAAarI,WAAWyI,GAGrC,GACEhJ,GAAQ,IAAcA,GAAQ,KAC3BA,GAAQ,IAAcA,GAAQ,IAC9BA,GAAQ,IAAmBA,GAAQ,IAC3B,KAATA,GACS,KAATA,GACS,KAATA,GACS,MAATA,GACC6I,GAAmB,KAAT7I,GACV8I,GAAwB,KAAT9I,GACf8I,GAAwB,KAAT9I,GACf8I,GAAwB,KAAT9I,GAGM,IAArB+I,IACH9I,GAAOgJ,mBAAmBL,EAAahB,UAAUmB,EAAiBC,IAClED,GAAmB,QAGR9H,IAARhB,IACHA,GAAO2I,EAAaM,OAAOF,QAGtB,MAEM/H,IAARhB,IACHA,EAAM2I,EAAaO,OAAO,EAAGH,IAI9B,MAAMI,EAAUV,EAAY1I,QACZiB,IAAZmI,IAGsB,IAArBL,IACH9I,GAAOgJ,mBAAmBL,EAAahB,UAAUmB,EAAiBC,IAClED,GAAmB,GAIpB9I,GAAOmJ,IAEwB,IAArBL,IAEVA,EAAkBC,E,EASrB,OAJyB,IAArBD,IACH9I,GAAOgJ,mBAAmBL,EAAahB,UAAUmB,UAGnC9H,IAARhB,EAAoBA,EAAM2I,CAClC,CAEA,SAASS,EAA0B3J,GAClC,IAAIO,EACJ,IAAK,IAAI+I,EAAM,EAAGA,EAAMtJ,EAAKY,OAAQ0I,IAAO,CAC3C,MAAMhJ,EAAON,EAAKa,WAAWyI,GAChB,KAAThJ,GAAmC,KAATA,QACjBiB,IAARhB,IACHA,EAAMP,EAAKyJ,OAAO,EAAGH,IAEtB/I,GAAOyI,EAAY1I,SAEPiB,IAARhB,IACHA,GAAOP,EAAKsJ,G,CAIf,YAAe/H,IAARhB,EAAoBA,EAAMP,CAClC,CAKO,SAASyH,EAAYmC,EAAUC,GAErC,IAAIlE,EAsBJ,OAnBCA,EAFGiE,EAAIpD,WAAaoD,EAAI5J,KAAKY,OAAS,GAAoB,SAAfgJ,EAAItD,OAEvC,KAAKsD,EAAIpD,YAAYoD,EAAI5J,OAEN,KAA3B4J,EAAI5J,KAAKa,WAAW,KAChB+I,EAAI5J,KAAKa,WAAW,IAAM,IAAc+I,EAAI5J,KAAKa,WAAW,IAAM,IAAc+I,EAAI5J,KAAKa,WAAW,IAAM,IAAc+I,EAAI5J,KAAKa,WAAW,IAAM,MACxH,KAA3B+I,EAAI5J,KAAKa,WAAW,GAElBgJ,EAIID,EAAI5J,KAAKyJ,OAAO,GAFhBG,EAAI5J,KAAK,GAAG8J,cAAgBF,EAAI5J,KAAKyJ,OAAO,GAM7CG,EAAI5J,KAET4F,IACHD,EAAQA,EAAMqC,QAAQ,MAAO,OAEvBrC,CACR,CAKA,SAAS2C,EAAasB,EAAUvB,GAE/B,MAAM0B,EAAW1B,EAEdsB,EADAV,EAGH,IAAI1I,EAAM,IACN,OAAE+F,EAAM,UAAEE,EAAS,KAAExG,EAAI,MAAEyG,EAAK,SAAEC,GAAakD,EASnD,GARItD,IACH/F,GAAO+F,EACP/F,GAAO,MAEJiG,GAAwB,SAAXF,KAChB/F,GAAOsG,EACPtG,GAAOsG,GAEJL,EAAW,CACd,IAAIyB,EAAMzB,EAAUR,QAAQ,KAC5B,IAAa,IAATiC,EAAY,CAEf,MAAM+B,EAAWxD,EAAUiD,OAAO,EAAGxB,GACrCzB,EAAYA,EAAUiD,OAAOxB,EAAM,GACnCA,EAAM+B,EAASjJ,YAAY,MACd,IAATkH,EACH1H,GAAOwJ,EAAQC,GAAU,GAAO,IAGhCzJ,GAAOwJ,EAAQC,EAASP,OAAO,EAAGxB,IAAM,GAAO,GAC/C1H,GAAO,IACPA,GAAOwJ,EAAQC,EAASP,OAAOxB,EAAM,IAAI,GAAO,IAEjD1H,GAAO,G,CAERiG,EAAYA,EAAUsD,cACtB7B,EAAMzB,EAAUzF,YAAY,MACf,IAATkH,EACH1H,GAAOwJ,EAAQvD,GAAW,GAAO,IAGjCjG,GAAOwJ,EAAQvD,EAAUiD,OAAO,EAAGxB,IAAM,GAAO,GAChD1H,GAAOiG,EAAUiD,OAAOxB,G,CAG1B,GAAIjI,EAAM,CAET,GAAIA,EAAKY,QAAU,GAA4B,KAAvBZ,EAAKa,WAAW,IAAgD,KAAvBb,EAAKa,WAAW,GAAuB,CACvG,MAAMP,EAAON,EAAKa,WAAW,GACzBP,GAAQ,IAAcA,GAAQ,KACjCN,EAAO,IAAIiK,OAAOC,aAAa5J,EAAO,OAAON,EAAKyJ,OAAO,K,MAEpD,GAAIzJ,EAAKY,QAAU,GAA4B,KAAvBZ,EAAKa,WAAW,GAAuB,CACrE,MAAMP,EAAON,EAAKa,WAAW,GACzBP,GAAQ,IAAcA,GAAQ,KACjCN,EAAO,GAAGiK,OAAOC,aAAa5J,EAAO,OAAON,EAAKyJ,OAAO,K,CAI1DlJ,GAAOwJ,EAAQ/J,GAAM,GAAM,E,CAU5B,OARIyG,IACHlG,GAAO,IACPA,GAAOwJ,EAAQtD,GAAO,GAAO,IAE1BC,IACHnG,GAAO,IACPA,GAAQ8H,EAAgE3B,EAAjDuC,EAAuBvC,GAAU,GAAO,IAEzDnG,CACR,CAIA,SAAS4J,EAA2BC,GACnC,IACC,OAAOC,mBAAmBD,E,CACzB,MACD,OAAIA,EAAIxJ,OAAS,EACTwJ,EAAIX,OAAO,EAAG,GAAKU,EAA2BC,EAAIX,OAAO,IAEzDW,C,CAGV,CAEA,MAAME,EAAiB,8BAEvB,SAASxC,EAAcsC,GACtB,OAAKA,EAAIxC,MAAM0C,GAGRF,EAAIpC,QAAQsC,GAAiB1C,GAAUuC,EAA2BvC,KAFjEwC,CAGT,C,aCjqBA,MAAMG,EAAY,SAAkB,EAC9BC,EAAQ,IAEP,IAAUC,GAAjB,SAAiBA,GAeG,EAAAC,SAAhB,SAAyBd,KAAae,GAClC,OAAOf,EAAIzC,KAAK,CAAEnH,KAAMuK,EAAU3I,KAAKgI,EAAI5J,QAAS2K,IACxD,EAgBgB,EAAAC,YAAhB,SAA4BhB,KAAae,GACrC,IAAI3K,EAAO4J,EAAI5J,KACX6K,GAAa,EACb7K,EAAK,KAAOwK,IACZxK,EAAOwK,EAAQxK,EACf6K,GAAa,GAEjB,IAAIzJ,EAAemJ,EAAUrJ,QAAQlB,KAAS2K,GAI9C,OAHIE,GAAczJ,EAAa,KAAOoJ,IAAUZ,EAAIpD,YAChDpF,EAAeA,EAAa8G,UAAU,IAEnC0B,EAAIzC,KAAK,CAAEnH,KAAMoB,GAC5B,EAUgB,EAAAuB,QAAhB,SAAwBiH,GACpB,GAAwB,IAApBA,EAAI5J,KAAKY,QAAgBgJ,EAAI5J,OAASwK,EACtC,OAAOZ,EAEX,IAAI5J,EAAOuK,EAAU5H,QAAQiH,EAAI5J,MAIjC,OAHoB,IAAhBA,EAAKY,QAAuC,KAAvBZ,EAAKa,WAAW,KACrCb,EAAO,IAEJ4J,EAAIzC,KAAK,CAAEnH,QACtB,EAUgB,EAAA+C,SAAhB,SAAyB6G,GACrB,OAAOW,EAAUxH,SAAS6G,EAAI5J,KAClC,EAUgB,EAAAoD,QAAhB,SAAwBwG,GACpB,OAAOW,EAAUnH,QAAQwG,EAAI5J,KACjC,CACH,CAzFD,CAAiByK,IAAAA,EAAK,K", "sources": ["webpack://LIB/./node_modules/path-browserify/index.js", "webpack://LIB/webpack/bootstrap", "webpack://LIB/webpack/runtime/define property getters", "webpack://LIB/webpack/runtime/hasOwnProperty shorthand", "webpack://LIB/webpack/runtime/make namespace object", "webpack://LIB/./src/platform.ts", "webpack://LIB/./src/uri.ts", "webpack://LIB/./src/utils.ts"], "sourcesContent": ["// 'path' module extracted from Node.js v8.11.1 (only the posix part)\n// transplited with Babel\n\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError('Path must be a string. Received ' + JSON.stringify(path));\n  }\n}\n\n// Resolves . and .. elements in a path with directory names\nfunction normalizeStringPosix(path, allowAboveRoot) {\n  var res = '';\n  var lastSegmentLength = 0;\n  var lastSlash = -1;\n  var dots = 0;\n  var code;\n  for (var i = 0; i <= path.length; ++i) {\n    if (i < path.length)\n      code = path.charCodeAt(i);\n    else if (code === 47 /*/*/)\n      break;\n    else\n      code = 47 /*/*/;\n    if (code === 47 /*/*/) {\n      if (lastSlash === i - 1 || dots === 1) {\n        // NOOP\n      } else if (lastSlash !== i - 1 && dots === 2) {\n        if (res.length < 2 || lastSegmentLength !== 2 || res.charCodeAt(res.length - 1) !== 46 /*.*/ || res.charCodeAt(res.length - 2) !== 46 /*.*/) {\n          if (res.length > 2) {\n            var lastSlashIndex = res.lastIndexOf('/');\n            if (lastSlashIndex !== res.length - 1) {\n              if (lastSlashIndex === -1) {\n                res = '';\n                lastSegmentLength = 0;\n              } else {\n                res = res.slice(0, lastSlashIndex);\n                lastSegmentLength = res.length - 1 - res.lastIndexOf('/');\n              }\n              lastSlash = i;\n              dots = 0;\n              continue;\n            }\n          } else if (res.length === 2 || res.length === 1) {\n            res = '';\n            lastSegmentLength = 0;\n            lastSlash = i;\n            dots = 0;\n            continue;\n          }\n        }\n        if (allowAboveRoot) {\n          if (res.length > 0)\n            res += '/..';\n          else\n            res = '..';\n          lastSegmentLength = 2;\n        }\n      } else {\n        if (res.length > 0)\n          res += '/' + path.slice(lastSlash + 1, i);\n        else\n          res = path.slice(lastSlash + 1, i);\n        lastSegmentLength = i - lastSlash - 1;\n      }\n      lastSlash = i;\n      dots = 0;\n    } else if (code === 46 /*.*/ && dots !== -1) {\n      ++dots;\n    } else {\n      dots = -1;\n    }\n  }\n  return res;\n}\n\nfunction _format(sep, pathObject) {\n  var dir = pathObject.dir || pathObject.root;\n  var base = pathObject.base || (pathObject.name || '') + (pathObject.ext || '');\n  if (!dir) {\n    return base;\n  }\n  if (dir === pathObject.root) {\n    return dir + base;\n  }\n  return dir + sep + base;\n}\n\nvar posix = {\n  // path.resolve([from ...], to)\n  resolve: function resolve() {\n    var resolvedPath = '';\n    var resolvedAbsolute = false;\n    var cwd;\n\n    for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n      var path;\n      if (i >= 0)\n        path = arguments[i];\n      else {\n        if (cwd === undefined)\n          cwd = process.cwd();\n        path = cwd;\n      }\n\n      assertPath(path);\n\n      // Skip empty entries\n      if (path.length === 0) {\n        continue;\n      }\n\n      resolvedPath = path + '/' + resolvedPath;\n      resolvedAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    }\n\n    // At this point the path should be resolved to a full absolute path, but\n    // handle relative paths to be safe (might happen when process.cwd() fails)\n\n    // Normalize the path\n    resolvedPath = normalizeStringPosix(resolvedPath, !resolvedAbsolute);\n\n    if (resolvedAbsolute) {\n      if (resolvedPath.length > 0)\n        return '/' + resolvedPath;\n      else\n        return '/';\n    } else if (resolvedPath.length > 0) {\n      return resolvedPath;\n    } else {\n      return '.';\n    }\n  },\n\n  normalize: function normalize(path) {\n    assertPath(path);\n\n    if (path.length === 0) return '.';\n\n    var isAbsolute = path.charCodeAt(0) === 47 /*/*/;\n    var trailingSeparator = path.charCodeAt(path.length - 1) === 47 /*/*/;\n\n    // Normalize the path\n    path = normalizeStringPosix(path, !isAbsolute);\n\n    if (path.length === 0 && !isAbsolute) path = '.';\n    if (path.length > 0 && trailingSeparator) path += '/';\n\n    if (isAbsolute) return '/' + path;\n    return path;\n  },\n\n  isAbsolute: function isAbsolute(path) {\n    assertPath(path);\n    return path.length > 0 && path.charCodeAt(0) === 47 /*/*/;\n  },\n\n  join: function join() {\n    if (arguments.length === 0)\n      return '.';\n    var joined;\n    for (var i = 0; i < arguments.length; ++i) {\n      var arg = arguments[i];\n      assertPath(arg);\n      if (arg.length > 0) {\n        if (joined === undefined)\n          joined = arg;\n        else\n          joined += '/' + arg;\n      }\n    }\n    if (joined === undefined)\n      return '.';\n    return posix.normalize(joined);\n  },\n\n  relative: function relative(from, to) {\n    assertPath(from);\n    assertPath(to);\n\n    if (from === to) return '';\n\n    from = posix.resolve(from);\n    to = posix.resolve(to);\n\n    if (from === to) return '';\n\n    // Trim any leading backslashes\n    var fromStart = 1;\n    for (; fromStart < from.length; ++fromStart) {\n      if (from.charCodeAt(fromStart) !== 47 /*/*/)\n        break;\n    }\n    var fromEnd = from.length;\n    var fromLen = fromEnd - fromStart;\n\n    // Trim any leading backslashes\n    var toStart = 1;\n    for (; toStart < to.length; ++toStart) {\n      if (to.charCodeAt(toStart) !== 47 /*/*/)\n        break;\n    }\n    var toEnd = to.length;\n    var toLen = toEnd - toStart;\n\n    // Compare paths to find the longest common path from root\n    var length = fromLen < toLen ? fromLen : toLen;\n    var lastCommonSep = -1;\n    var i = 0;\n    for (; i <= length; ++i) {\n      if (i === length) {\n        if (toLen > length) {\n          if (to.charCodeAt(toStart + i) === 47 /*/*/) {\n            // We get here if `from` is the exact base path for `to`.\n            // For example: from='/foo/bar'; to='/foo/bar/baz'\n            return to.slice(toStart + i + 1);\n          } else if (i === 0) {\n            // We get here if `from` is the root\n            // For example: from='/'; to='/foo'\n            return to.slice(toStart + i);\n          }\n        } else if (fromLen > length) {\n          if (from.charCodeAt(fromStart + i) === 47 /*/*/) {\n            // We get here if `to` is the exact base path for `from`.\n            // For example: from='/foo/bar/baz'; to='/foo/bar'\n            lastCommonSep = i;\n          } else if (i === 0) {\n            // We get here if `to` is the root.\n            // For example: from='/foo'; to='/'\n            lastCommonSep = 0;\n          }\n        }\n        break;\n      }\n      var fromCode = from.charCodeAt(fromStart + i);\n      var toCode = to.charCodeAt(toStart + i);\n      if (fromCode !== toCode)\n        break;\n      else if (fromCode === 47 /*/*/)\n        lastCommonSep = i;\n    }\n\n    var out = '';\n    // Generate the relative path based on the path difference between `to`\n    // and `from`\n    for (i = fromStart + lastCommonSep + 1; i <= fromEnd; ++i) {\n      if (i === fromEnd || from.charCodeAt(i) === 47 /*/*/) {\n        if (out.length === 0)\n          out += '..';\n        else\n          out += '/..';\n      }\n    }\n\n    // Lastly, append the rest of the destination (`to`) path that comes after\n    // the common path parts\n    if (out.length > 0)\n      return out + to.slice(toStart + lastCommonSep);\n    else {\n      toStart += lastCommonSep;\n      if (to.charCodeAt(toStart) === 47 /*/*/)\n        ++toStart;\n      return to.slice(toStart);\n    }\n  },\n\n  _makeLong: function _makeLong(path) {\n    return path;\n  },\n\n  dirname: function dirname(path) {\n    assertPath(path);\n    if (path.length === 0) return '.';\n    var code = path.charCodeAt(0);\n    var hasRoot = code === 47 /*/*/;\n    var end = -1;\n    var matchedSlash = true;\n    for (var i = path.length - 1; i >= 1; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          if (!matchedSlash) {\n            end = i;\n            break;\n          }\n        } else {\n        // We saw the first non-path separator\n        matchedSlash = false;\n      }\n    }\n\n    if (end === -1) return hasRoot ? '/' : '.';\n    if (hasRoot && end === 1) return '//';\n    return path.slice(0, end);\n  },\n\n  basename: function basename(path, ext) {\n    if (ext !== undefined && typeof ext !== 'string') throw new TypeError('\"ext\" argument must be a string');\n    assertPath(path);\n\n    var start = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i;\n\n    if (ext !== undefined && ext.length > 0 && ext.length <= path.length) {\n      if (ext.length === path.length && ext === path) return '';\n      var extIdx = ext.length - 1;\n      var firstNonSlashEnd = -1;\n      for (i = path.length - 1; i >= 0; --i) {\n        var code = path.charCodeAt(i);\n        if (code === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else {\n          if (firstNonSlashEnd === -1) {\n            // We saw the first non-path separator, remember this index in case\n            // we need it if the extension ends up not matching\n            matchedSlash = false;\n            firstNonSlashEnd = i + 1;\n          }\n          if (extIdx >= 0) {\n            // Try to match the explicit extension\n            if (code === ext.charCodeAt(extIdx)) {\n              if (--extIdx === -1) {\n                // We matched the extension, so mark this as the end of our path\n                // component\n                end = i;\n              }\n            } else {\n              // Extension does not match, so our result is the entire path\n              // component\n              extIdx = -1;\n              end = firstNonSlashEnd;\n            }\n          }\n        }\n      }\n\n      if (start === end) end = firstNonSlashEnd;else if (end === -1) end = path.length;\n      return path.slice(start, end);\n    } else {\n      for (i = path.length - 1; i >= 0; --i) {\n        if (path.charCodeAt(i) === 47 /*/*/) {\n            // If we reached a path separator that was not part of a set of path\n            // separators at the end of the string, stop now\n            if (!matchedSlash) {\n              start = i + 1;\n              break;\n            }\n          } else if (end === -1) {\n          // We saw the first non-path separator, mark this as the end of our\n          // path component\n          matchedSlash = false;\n          end = i + 1;\n        }\n      }\n\n      if (end === -1) return '';\n      return path.slice(start, end);\n    }\n  },\n\n  extname: function extname(path) {\n    assertPath(path);\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n    for (var i = path.length - 1; i >= 0; --i) {\n      var code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1)\n            startDot = i;\n          else if (preDotState !== 1)\n            preDotState = 1;\n      } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n        // We saw a non-dot character immediately before the dot\n        preDotState === 0 ||\n        // The (right-most) trimmed path component is exactly '..'\n        preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      return '';\n    }\n    return path.slice(startDot, end);\n  },\n\n  format: function format(pathObject) {\n    if (pathObject === null || typeof pathObject !== 'object') {\n      throw new TypeError('The \"pathObject\" argument must be of type Object. Received type ' + typeof pathObject);\n    }\n    return _format('/', pathObject);\n  },\n\n  parse: function parse(path) {\n    assertPath(path);\n\n    var ret = { root: '', dir: '', base: '', ext: '', name: '' };\n    if (path.length === 0) return ret;\n    var code = path.charCodeAt(0);\n    var isAbsolute = code === 47 /*/*/;\n    var start;\n    if (isAbsolute) {\n      ret.root = '/';\n      start = 1;\n    } else {\n      start = 0;\n    }\n    var startDot = -1;\n    var startPart = 0;\n    var end = -1;\n    var matchedSlash = true;\n    var i = path.length - 1;\n\n    // Track the state of characters (if any) we see before our first dot and\n    // after any path separator we find\n    var preDotState = 0;\n\n    // Get non-dir info\n    for (; i >= start; --i) {\n      code = path.charCodeAt(i);\n      if (code === 47 /*/*/) {\n          // If we reached a path separator that was not part of a set of path\n          // separators at the end of the string, stop now\n          if (!matchedSlash) {\n            startPart = i + 1;\n            break;\n          }\n          continue;\n        }\n      if (end === -1) {\n        // We saw the first non-path separator, mark this as the end of our\n        // extension\n        matchedSlash = false;\n        end = i + 1;\n      }\n      if (code === 46 /*.*/) {\n          // If this is our first dot, mark it as the start of our extension\n          if (startDot === -1) startDot = i;else if (preDotState !== 1) preDotState = 1;\n        } else if (startDot !== -1) {\n        // We saw a non-dot and non-path separator before our dot, so we should\n        // have a good chance at having a non-empty extension\n        preDotState = -1;\n      }\n    }\n\n    if (startDot === -1 || end === -1 ||\n    // We saw a non-dot character immediately before the dot\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly '..'\n    preDotState === 1 && startDot === end - 1 && startDot === startPart + 1) {\n      if (end !== -1) {\n        if (startPart === 0 && isAbsolute) ret.base = ret.name = path.slice(1, end);else ret.base = ret.name = path.slice(startPart, end);\n      }\n    } else {\n      if (startPart === 0 && isAbsolute) {\n        ret.name = path.slice(1, startDot);\n        ret.base = path.slice(1, end);\n      } else {\n        ret.name = path.slice(startPart, startDot);\n        ret.base = path.slice(startPart, end);\n      }\n      ret.ext = path.slice(startDot, end);\n    }\n\n    if (startPart > 0) ret.dir = path.slice(0, startPart - 1);else if (isAbsolute) ret.dir = '/';\n\n    return ret;\n  },\n\n  sep: '/',\n  delimiter: ':',\n  win32: null,\n  posix: null\n};\n\nposix.posix = posix;\n\nmodule.exports = posix;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\n\n// !!!!!\n// SEE https://github.com/microsoft/vscode/blob/master/src/vs/base/common/platform.ts\n// !!!!!\n\ndeclare const process: { platform: 'win32' };\ndeclare const navigator: { userAgent: string };\n\nexport let isWindows: boolean;\n\nif (typeof process === 'object') {\n\tisWindows = process.platform === 'win32';\n} else if (typeof navigator === 'object') {\n\tlet userAgent = navigator.userAgent;\n\tisWindows = userAgent.indexOf('Windows') >= 0;\n}\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n'use strict';\n\nimport { CharCode } from './charCode'\nimport { isWindows } from './platform';\n\nconst _schemePattern = /^\\w[\\w\\d+.-]*$/;\nconst _singleSlashStart = /^\\//;\nconst _doubleSlashStart = /^\\/\\//;\n\nfunction _validateUri(ret: URI, _strict?: boolean): void {\n\n\t// scheme, must be set\n\tif (!ret.scheme && _strict) {\n\t\tthrow new Error(`[UriError]: Scheme is missing: {scheme: \"\", authority: \"${ret.authority}\", path: \"${ret.path}\", query: \"${ret.query}\", fragment: \"${ret.fragment}\"}`);\n\t}\n\n\t// scheme, https://tools.ietf.org/html/rfc3986#section-3.1\n\t// ALPHA *( ALPHA / DIGIT / \"+\" / \"-\" / \".\" )\n\tif (ret.scheme && !_schemePattern.test(ret.scheme)) {\n\t\tthrow new Error('[UriError]: Scheme contains illegal characters.');\n\t}\n\n\t// path, http://tools.ietf.org/html/rfc3986#section-3.3\n\t// If a URI contains an authority component, then the path component\n\t// must either be empty or begin with a slash (\"/\") character.  If a URI\n\t// does not contain an authority component, then the path cannot begin\n\t// with two slash characters (\"//\").\n\tif (ret.path) {\n\t\tif (ret.authority) {\n\t\t\tif (!_singleSlashStart.test(ret.path)) {\n\t\t\t\tthrow new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash (\"/\") character');\n\t\t\t}\n\t\t} else {\n\t\t\tif (_doubleSlashStart.test(ret.path)) {\n\t\t\t\tthrow new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters (\"//\")');\n\t\t\t}\n\t\t}\n\t}\n}\n\n// for a while we allowed uris *without* schemes and this is the migration\n// for them, e.g. an uri without scheme and without strict-mode warns and falls\n// back to the file-scheme. that should cause the least carnage and still be a\n// clear warning\nfunction _schemeFix(scheme: string, _strict: boolean): string {\n\tif (!scheme && !_strict) {\n\t\treturn 'file';\n\t}\n\treturn scheme;\n}\n\n// implements a bit of https://tools.ietf.org/html/rfc3986#section-5\nfunction _referenceResolution(scheme: string, path: string): string {\n\n\t// the slash-character is our 'default base' as we don't\n\t// support constructing URIs relative to other URIs. This\n\t// also means that we alter and potentially break paths.\n\t// see https://tools.ietf.org/html/rfc3986#section-5.1.4\n\tswitch (scheme) {\n\t\tcase 'https':\n\t\tcase 'http':\n\t\tcase 'file':\n\t\t\tif (!path) {\n\t\t\t\tpath = _slash;\n\t\t\t} else if (path[0] !== _slash) {\n\t\t\t\tpath = _slash + path;\n\t\t\t}\n\t\t\tbreak;\n\t}\n\treturn path;\n}\n\nconst _empty = '';\nconst _slash = '/';\nconst _regexp = /^(([^:/?#]+?):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\n\n/**\n * Uniform Resource Identifier (URI) http://tools.ietf.org/html/rfc3986.\n * This class is a simple parser which creates the basic component parts\n * (http://tools.ietf.org/html/rfc3986#section-3) with minimal validation\n * and encoding.\n *\n * ```txt\n *       foo://example.com:8042/over/there?name=ferret#nose\n *       \\_/   \\______________/\\_________/ \\_________/ \\__/\n *        |           |            |            |        |\n *     scheme     authority       path        query   fragment\n *        |   _____________________|__\n *       / \\ /                        \\\n *       urn:example:animal:ferret:nose\n * ```\n */\nexport class URI implements UriComponents {\n\n\tstatic isUri(thing: any): thing is URI {\n\t\tif (thing instanceof URI) {\n\t\t\treturn true;\n\t\t}\n\t\tif (!thing) {\n\t\t\treturn false;\n\t\t}\n\t\treturn typeof (<URI>thing).authority === 'string'\n\t\t\t&& typeof (<URI>thing).fragment === 'string'\n\t\t\t&& typeof (<URI>thing).path === 'string'\n\t\t\t&& typeof (<URI>thing).query === 'string'\n\t\t\t&& typeof (<URI>thing).scheme === 'string'\n\t\t\t&& typeof (<URI>thing).fsPath === 'string'\n\t\t\t&& typeof (<URI>thing).with === 'function'\n\t\t\t&& typeof (<URI>thing).toString === 'function';\n\t}\n\n\t/**\n\t * scheme is the 'http' part of 'http://www.example.com/some/path?query#fragment'.\n\t * The part before the first colon.\n\t */\n\treadonly scheme: string;\n\n\t/**\n\t * authority is the 'www.example.com' part of 'http://www.example.com/some/path?query#fragment'.\n\t * The part between the first double slashes and the next slash.\n\t */\n\treadonly authority: string;\n\n\t/**\n\t * path is the '/some/path' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly path: string;\n\n\t/**\n\t * query is the 'query' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly query: string;\n\n\t/**\n\t * fragment is the 'fragment' part of 'http://www.example.com/some/path?query#fragment'.\n\t */\n\treadonly fragment: string;\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(scheme: string, authority?: string, path?: string, query?: string, fragment?: string, _strict?: boolean);\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(components: UriComponents);\n\n\t/**\n\t * @internal\n\t */\n\tprotected constructor(schemeOrData: string | UriComponents, authority?: string, path?: string, query?: string, fragment?: string, _strict: boolean = false) {\n\n\t\tif (typeof schemeOrData === 'object') {\n\t\t\tthis.scheme = schemeOrData.scheme || _empty;\n\t\t\tthis.authority = schemeOrData.authority || _empty;\n\t\t\tthis.path = schemeOrData.path || _empty;\n\t\t\tthis.query = schemeOrData.query || _empty;\n\t\t\tthis.fragment = schemeOrData.fragment || _empty;\n\t\t\t// no validation because it's this URI\n\t\t\t// that creates uri components.\n\t\t\t// _validateUri(this);\n\t\t} else {\n\t\t\tthis.scheme = _schemeFix(schemeOrData, _strict);\n\t\t\tthis.authority = authority || _empty;\n\t\t\tthis.path = _referenceResolution(this.scheme, path || _empty);\n\t\t\tthis.query = query || _empty;\n\t\t\tthis.fragment = fragment || _empty;\n\n\t\t\t_validateUri(this, _strict);\n\t\t}\n\t}\n\n\t// ---- filesystem path -----------------------\n\n\t/**\n\t * Returns a string representing the corresponding file system path of this URI.\n\t * Will handle UNC paths, normalizes windows drive letters to lower-case, and uses the\n\t * platform specific path separator.\n\t *\n\t * * Will *not* validate the path for invalid characters and semantics.\n\t * * Will *not* look at the scheme of this URI.\n\t * * The result shall *not* be used for display purposes but for accessing a file on disk.\n\t *\n\t *\n\t * The *difference* to `URI#path` is the use of the platform specific separator and the handling\n\t * of UNC paths. See the below sample of a file-uri with an authority (UNC path).\n\t *\n\t * ```ts\n\t\tconst u = URI.parse('file://server/c$/folder/file.txt')\n\t\tu.authority === 'server'\n\t\tu.path === '/shares/c$/file.txt'\n\t\tu.fsPath === '\\\\server\\c$\\folder\\file.txt'\n\t```\n\t *\n\t * Using `URI#path` to read a file (using fs-apis) would not be enough because parts of the path,\n\t * namely the server name, would be missing. Therefore `URI#fsPath` exists - it's sugar to ease working\n\t * with URIs that represent files on disk (`file` scheme).\n\t */\n\tget fsPath(): string {\n\t\t// if (this.scheme !== 'file') {\n\t\t// \tconsole.warn(`[UriError] calling fsPath with scheme ${this.scheme}`);\n\t\t// }\n\t\treturn uriToFsPath(this, false);\n\t}\n\n\t// ---- modify to new -------------------------\n\n\twith(change: { scheme?: string; authority?: string | null; path?: string | null; query?: string | null; fragment?: string | null }): URI {\n\n\t\tif (!change) {\n\t\t\treturn this;\n\t\t}\n\n\t\tlet { scheme, authority, path, query, fragment } = change;\n\t\tif (scheme === undefined) {\n\t\t\tscheme = this.scheme;\n\t\t} else if (scheme === null) {\n\t\t\tscheme = _empty;\n\t\t}\n\t\tif (authority === undefined) {\n\t\t\tauthority = this.authority;\n\t\t} else if (authority === null) {\n\t\t\tauthority = _empty;\n\t\t}\n\t\tif (path === undefined) {\n\t\t\tpath = this.path;\n\t\t} else if (path === null) {\n\t\t\tpath = _empty;\n\t\t}\n\t\tif (query === undefined) {\n\t\t\tquery = this.query;\n\t\t} else if (query === null) {\n\t\t\tquery = _empty;\n\t\t}\n\t\tif (fragment === undefined) {\n\t\t\tfragment = this.fragment;\n\t\t} else if (fragment === null) {\n\t\t\tfragment = _empty;\n\t\t}\n\n\t\tif (scheme === this.scheme\n\t\t\t&& authority === this.authority\n\t\t\t&& path === this.path\n\t\t\t&& query === this.query\n\t\t\t&& fragment === this.fragment) {\n\n\t\t\treturn this;\n\t\t}\n\n\t\treturn new Uri(scheme, authority, path, query, fragment);\n\t}\n\n\t// ---- parse & validate ------------------------\n\n\t/**\n\t * Creates a new URI from a string, e.g. `http://www.example.com/some/path`,\n\t * `file:///usr/home`, or `scheme:with/path`.\n\t *\n\t * @param value A string which represents an URI (see `URI#toString`).\n\t */\n\tstatic parse(value: string, _strict: boolean = false): URI {\n\t\tconst match = _regexp.exec(value);\n\t\tif (!match) {\n\t\t\treturn new Uri(_empty, _empty, _empty, _empty, _empty);\n\t\t}\n\t\treturn new Uri(\n\t\t\tmatch[2] || _empty,\n\t\t\tpercentDecode(match[4] || _empty),\n\t\t\tpercentDecode(match[5] || _empty),\n\t\t\tpercentDecode(match[7] || _empty),\n\t\t\tpercentDecode(match[9] || _empty),\n\t\t\t_strict\n\t\t);\n\t}\n\n\t/**\n\t * Creates a new URI from a file system path, e.g. `c:\\my\\files`,\n\t * `/usr/home`, or `\\\\server\\share\\some\\path`.\n\t *\n\t * The *difference* between `URI#parse` and `URI#file` is that the latter treats the argument\n\t * as path, not as stringified-uri. E.g. `URI.file(path)` is **not the same as**\n\t * `URI.parse('file://' + path)` because the path might contain characters that are\n\t * interpreted (# and ?). See the following sample:\n\t * ```ts\n\tconst good = URI.file('/coding/c#/project1');\n\tgood.scheme === 'file';\n\tgood.path === '/coding/c#/project1';\n\tgood.fragment === '';\n\tconst bad = URI.parse('file://' + '/coding/c#/project1');\n\tbad.scheme === 'file';\n\tbad.path === '/coding/c'; // path is now broken\n\tbad.fragment === '/project1';\n\t```\n\t *\n\t * @param path A file system path (see `URI#fsPath`)\n\t */\n\tstatic file(path: string): URI {\n\n\t\tlet authority = _empty;\n\n\t\t// normalize to fwd-slashes on windows,\n\t\t// on other systems bwd-slashes are valid\n\t\t// filename character, eg /f\\oo/ba\\r.txt\n\t\tif (isWindows) {\n\t\t\tpath = path.replace(/\\\\/g, _slash);\n\t\t}\n\n\t\t// check for authority as used in UNC shares\n\t\t// or use the path as given\n\t\tif (path[0] === _slash && path[1] === _slash) {\n\t\t\tconst idx = path.indexOf(_slash, 2);\n\t\t\tif (idx === -1) {\n\t\t\t\tauthority = path.substring(2);\n\t\t\t\tpath = _slash;\n\t\t\t} else {\n\t\t\t\tauthority = path.substring(2, idx);\n\t\t\t\tpath = path.substring(idx) || _slash;\n\t\t\t}\n\t\t}\n\n\t\treturn new Uri('file', authority, path, _empty, _empty);\n\t}\n\n\tstatic from(components: { scheme: string; authority?: string; path?: string; query?: string; fragment?: string }): URI {\n\t\tconst result = new Uri(\n\t\t\tcomponents.scheme,\n\t\t\tcomponents.authority,\n\t\t\tcomponents.path,\n\t\t\tcomponents.query,\n\t\t\tcomponents.fragment,\n\t\t);\n\t\t_validateUri(result, true);\n\t\treturn result;\n\t}\n\n\t// ---- printing/externalize ---------------------------\n\n\t/**\n\t * Creates a string representation for this URI. It's guaranteed that calling\n\t * `URI.parse` with the result of this function creates an URI which is equal\n\t * to this URI.\n\t *\n\t * * The result shall *not* be used for display purposes but for externalization or transport.\n\t * * The result will be encoded using the percentage encoding and encoding happens mostly\n\t * ignore the scheme-specific encoding rules.\n\t *\n\t * @param skipEncoding Do not encode the result, default is `false`\n\t */\n\ttoString(skipEncoding: boolean = false): string {\n\t\treturn _asFormatted(this, skipEncoding);\n\t}\n\n\ttoJSON(): UriComponents {\n\t\treturn this;\n\t}\n\n\tstatic revive(data: UriComponents | URI): URI;\n\tstatic revive(data: UriComponents | URI | undefined): URI | undefined;\n\tstatic revive(data: UriComponents | URI | null): URI | null;\n\tstatic revive(data: UriComponents | URI | undefined | null): URI | undefined | null;\n\tstatic revive(data: UriComponents | URI | undefined | null): URI | undefined | null {\n\t\tif (!data) {\n\t\t\treturn <any>data;\n\t\t} else if (data instanceof URI) {\n\t\t\treturn data;\n\t\t} else {\n\t\t\tconst result = new Uri(data);\n\t\t\tresult._formatted = (<UriState>data).external;\n\t\t\tresult._fsPath = (<UriState>data)._sep === _pathSepMarker ? (<UriState>data).fsPath : null;\n\t\t\treturn result;\n\t\t}\n\t}\n}\n\nexport interface UriComponents {\n\tscheme: string;\n\tauthority: string;\n\tpath: string;\n\tquery: string;\n\tfragment: string;\n}\n\ninterface UriState extends UriComponents {\n\t$mid: number;\n\texternal: string;\n\tfsPath: string;\n\t_sep: 1 | undefined;\n}\n\nconst _pathSepMarker = isWindows ? 1 : undefined;\n\n// This class exists so that URI is compatible with vscode.Uri (API).\nclass Uri extends URI {\n\n\t_formatted: string | null = null;\n\t_fsPath: string | null = null;\n\n\toverride get fsPath(): string {\n\t\tif (!this._fsPath) {\n\t\t\tthis._fsPath = uriToFsPath(this, false);\n\t\t}\n\t\treturn this._fsPath;\n\t}\n\n\toverride toString(skipEncoding: boolean = false): string {\n\t\tif (!skipEncoding) {\n\t\t\tif (!this._formatted) {\n\t\t\t\tthis._formatted = _asFormatted(this, false);\n\t\t\t}\n\t\t\treturn this._formatted;\n\t\t} else {\n\t\t\t// we don't cache that\n\t\t\treturn _asFormatted(this, true);\n\t\t}\n\t}\n\n\toverride toJSON(): UriComponents {\n\t\tconst res = <UriState>{\n\t\t\t$mid: 1\n\t\t};\n\t\t// cached state\n\t\tif (this._fsPath) {\n\t\t\tres.fsPath = this._fsPath;\n\t\t\tres._sep = _pathSepMarker;\n\t\t}\n\t\tif (this._formatted) {\n\t\t\tres.external = this._formatted;\n\t\t}\n\t\t// uri components\n\t\tif (this.path) {\n\t\t\tres.path = this.path;\n\t\t}\n\t\tif (this.scheme) {\n\t\t\tres.scheme = this.scheme;\n\t\t}\n\t\tif (this.authority) {\n\t\t\tres.authority = this.authority;\n\t\t}\n\t\tif (this.query) {\n\t\t\tres.query = this.query;\n\t\t}\n\t\tif (this.fragment) {\n\t\t\tres.fragment = this.fragment;\n\t\t}\n\t\treturn res;\n\t}\n}\n\n// reserved characters: https://tools.ietf.org/html/rfc3986#section-2.2\nconst encodeTable: { [ch: number]: string } = {\n\t[CharCode.Colon]: '%3A', // gen-delims\n\t[CharCode.Slash]: '%2F',\n\t[CharCode.QuestionMark]: '%3F',\n\t[CharCode.Hash]: '%23',\n\t[CharCode.OpenSquareBracket]: '%5B',\n\t[CharCode.CloseSquareBracket]: '%5D',\n\t[CharCode.AtSign]: '%40',\n\n\t[CharCode.ExclamationMark]: '%21', // sub-delims\n\t[CharCode.DollarSign]: '%24',\n\t[CharCode.Ampersand]: '%26',\n\t[CharCode.SingleQuote]: '%27',\n\t[CharCode.OpenParen]: '%28',\n\t[CharCode.CloseParen]: '%29',\n\t[CharCode.Asterisk]: '%2A',\n\t[CharCode.Plus]: '%2B',\n\t[CharCode.Comma]: '%2C',\n\t[CharCode.Semicolon]: '%3B',\n\t[CharCode.Equals]: '%3D',\n\n\t[CharCode.Space]: '%20',\n};\n\nfunction encodeURIComponentFast(uriComponent: string, isPath: boolean, isAuthority: boolean): string {\n\tlet res: string | undefined = undefined;\n\tlet nativeEncodePos = -1;\n\n\tfor (let pos = 0; pos < uriComponent.length; pos++) {\n\t\tconst code = uriComponent.charCodeAt(pos);\n\n\t\t// unreserved characters: https://tools.ietf.org/html/rfc3986#section-2.3\n\t\tif (\n\t\t\t(code >= CharCode.a && code <= CharCode.z)\n\t\t\t|| (code >= CharCode.A && code <= CharCode.Z)\n\t\t\t|| (code >= CharCode.Digit0 && code <= CharCode.Digit9)\n\t\t\t|| code === CharCode.Dash\n\t\t\t|| code === CharCode.Period\n\t\t\t|| code === CharCode.Underline\n\t\t\t|| code === CharCode.Tilde\n\t\t\t|| (isPath && code === CharCode.Slash)\n\t\t\t|| (isAuthority && code === CharCode.OpenSquareBracket)\n\t\t\t|| (isAuthority && code === CharCode.CloseSquareBracket)\n\t\t\t|| (isAuthority && code === CharCode.Colon)\n\t\t) {\n\t\t\t// check if we are delaying native encode\n\t\t\tif (nativeEncodePos !== -1) {\n\t\t\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos, pos));\n\t\t\t\tnativeEncodePos = -1;\n\t\t\t}\n\t\t\t// check if we write into a new string (by default we try to return the param)\n\t\t\tif (res !== undefined) {\n\t\t\t\tres += uriComponent.charAt(pos);\n\t\t\t}\n\n\t\t} else {\n\t\t\t// encoding needed, we need to allocate a new string\n\t\t\tif (res === undefined) {\n\t\t\t\tres = uriComponent.substr(0, pos);\n\t\t\t}\n\n\t\t\t// check with default table first\n\t\t\tconst escaped = encodeTable[code];\n\t\t\tif (escaped !== undefined) {\n\n\t\t\t\t// check if we are delaying native encode\n\t\t\t\tif (nativeEncodePos !== -1) {\n\t\t\t\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos, pos));\n\t\t\t\t\tnativeEncodePos = -1;\n\t\t\t\t}\n\n\t\t\t\t// append escaped variant to result\n\t\t\t\tres += escaped;\n\n\t\t\t} else if (nativeEncodePos === -1) {\n\t\t\t\t// use native encode only when needed\n\t\t\t\tnativeEncodePos = pos;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (nativeEncodePos !== -1) {\n\t\tres += encodeURIComponent(uriComponent.substring(nativeEncodePos));\n\t}\n\n\treturn res !== undefined ? res : uriComponent;\n}\n\nfunction encodeURIComponentMinimal(path: string): string {\n\tlet res: string | undefined = undefined;\n\tfor (let pos = 0; pos < path.length; pos++) {\n\t\tconst code = path.charCodeAt(pos);\n\t\tif (code === CharCode.Hash || code === CharCode.QuestionMark) {\n\t\t\tif (res === undefined) {\n\t\t\t\tres = path.substr(0, pos);\n\t\t\t}\n\t\t\tres += encodeTable[code];\n\t\t} else {\n\t\t\tif (res !== undefined) {\n\t\t\t\tres += path[pos];\n\t\t\t}\n\t\t}\n\t}\n\treturn res !== undefined ? res : path;\n}\n\n/**\n * Compute `fsPath` for the given uri\n */\nexport function uriToFsPath(uri: URI, keepDriveLetterCasing: boolean): string {\n\n\tlet value: string;\n\tif (uri.authority && uri.path.length > 1 && uri.scheme === 'file') {\n\t\t// unc path: file://shares/c$/far/boo\n\t\tvalue = `//${uri.authority}${uri.path}`;\n\t} else if (\n\t\turi.path.charCodeAt(0) === CharCode.Slash\n\t\t&& (uri.path.charCodeAt(1) >= CharCode.A && uri.path.charCodeAt(1) <= CharCode.Z || uri.path.charCodeAt(1) >= CharCode.a && uri.path.charCodeAt(1) <= CharCode.z)\n\t\t&& uri.path.charCodeAt(2) === CharCode.Colon\n\t) {\n\t\tif (!keepDriveLetterCasing) {\n\t\t\t// windows drive letter: file:///c:/far/boo\n\t\t\tvalue = uri.path[1].toLowerCase() + uri.path.substr(2);\n\t\t} else {\n\t\t\tvalue = uri.path.substr(1);\n\t\t}\n\t} else {\n\t\t// other path\n\t\tvalue = uri.path;\n\t}\n\tif (isWindows) {\n\t\tvalue = value.replace(/\\//g, '\\\\');\n\t}\n\treturn value;\n}\n\n/**\n * Create the external version of a uri\n */\nfunction _asFormatted(uri: URI, skipEncoding: boolean): string {\n\n\tconst encoder = !skipEncoding\n\t\t? encodeURIComponentFast\n\t\t: encodeURIComponentMinimal;\n\n\tlet res = '';\n\tlet { scheme, authority, path, query, fragment } = uri;\n\tif (scheme) {\n\t\tres += scheme;\n\t\tres += ':';\n\t}\n\tif (authority || scheme === 'file') {\n\t\tres += _slash;\n\t\tres += _slash;\n\t}\n\tif (authority) {\n\t\tlet idx = authority.indexOf('@');\n\t\tif (idx !== -1) {\n\t\t\t// <user>@<auth>\n\t\t\tconst userinfo = authority.substr(0, idx);\n\t\t\tauthority = authority.substr(idx + 1);\n\t\t\tidx = userinfo.lastIndexOf(':');\n\t\t\tif (idx === -1) {\n\t\t\t\tres += encoder(userinfo, false, false);\n\t\t\t} else {\n\t\t\t\t// <user>:<pass>@<auth>\n\t\t\t\tres += encoder(userinfo.substr(0, idx), false, false);\n\t\t\t\tres += ':';\n\t\t\t\tres += encoder(userinfo.substr(idx + 1), false, true);\n\t\t\t}\n\t\t\tres += '@';\n\t\t}\n\t\tauthority = authority.toLowerCase();\n\t\tidx = authority.lastIndexOf(':');\n\t\tif (idx === -1) {\n\t\t\tres += encoder(authority, false, true);\n\t\t} else {\n\t\t\t// <auth>:<port>\n\t\t\tres += encoder(authority.substr(0, idx), false, true);\n\t\t\tres += authority.substr(idx);\n\t\t}\n\t}\n\tif (path) {\n\t\t// lower-case windows drive letters in /C:/fff or C:/fff\n\t\tif (path.length >= 3 && path.charCodeAt(0) === CharCode.Slash && path.charCodeAt(2) === CharCode.Colon) {\n\t\t\tconst code = path.charCodeAt(1);\n\t\t\tif (code >= CharCode.A && code <= CharCode.Z) {\n\t\t\t\tpath = `/${String.fromCharCode(code + 32)}:${path.substr(3)}`; // \"/c:\".length === 3\n\t\t\t}\n\t\t} else if (path.length >= 2 && path.charCodeAt(1) === CharCode.Colon) {\n\t\t\tconst code = path.charCodeAt(0);\n\t\t\tif (code >= CharCode.A && code <= CharCode.Z) {\n\t\t\t\tpath = `${String.fromCharCode(code + 32)}:${path.substr(2)}`; // \"/c:\".length === 3\n\t\t\t}\n\t\t}\n\t\t// encode the rest of the path\n\t\tres += encoder(path, true, false);\n\t}\n\tif (query) {\n\t\tres += '?';\n\t\tres += encoder(query, false, false);\n\t}\n\tif (fragment) {\n\t\tres += '#';\n\t\tres += !skipEncoding ? encodeURIComponentFast(fragment, false, false) : fragment;\n\t}\n\treturn res;\n}\n\n// --- decode\n\nfunction decodeURIComponentGraceful(str: string): string {\n\ttry {\n\t\treturn decodeURIComponent(str);\n\t} catch {\n\t\tif (str.length > 3) {\n\t\t\treturn str.substr(0, 3) + decodeURIComponentGraceful(str.substr(3));\n\t\t} else {\n\t\t\treturn str;\n\t\t}\n\t}\n}\n\nconst _rEncodedAsHex = /(%[0-9A-Za-z][0-9A-Za-z])+/g;\n\nfunction percentDecode(str: string): string {\n\tif (!str.match(_rEncodedAsHex)) {\n\t\treturn str;\n\t}\n\treturn str.replace(_rEncodedAsHex, (match) => decodeURIComponentGraceful(match));\n}\n\n/**\n * Mapped-type that replaces all occurrences of URI with UriComponents\n */\nexport type UriDto<T> = { [K in keyof T]: T[K] extends URI\n\t? UriComponents\n\t: UriDto<T[K]> };\n", "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation. All rights reserved.\n *  Licensed under the MIT License. See License.txt in the project root for license information.\n *--------------------------------------------------------------------------------------------*/\n\n'use strict';\n\nimport { CharCode } from './charCode';\nimport { URI } from './uri';\nimport * as nodePath from 'path';\n\nconst posixPath = nodePath.posix || nodePath;\nconst slash = '/';\n\nexport namespace Utils {\n\n    /**\n     * Joins one or more input paths to the path of URI. \n     * '/' is used as the directory separation character. \n     * \n     * The resolved path will be normalized. That means:\n     *  - all '..' and '.' segments are resolved.\n     *  - multiple, sequential occurences of '/' are replaced by a single instance of '/'.\n     *  - trailing separators are preserved.\n     * \n     * @param uri The input URI.\n     * @param paths The paths to be joined with the path of URI.\n     * @returns A URI with the joined path. All other properties of the URI (scheme, authority, query, fragments, ...) will be taken from the input URI.\n     */\n    export function joinPath(uri: URI, ...paths: string[]): URI {\n        return uri.with({ path: posixPath.join(uri.path, ...paths) });\n    }\n\n\n    /**\n     * Resolves one or more paths against the path of a URI. \n     * '/' is used as the directory separation character. \n     * \n     * The resolved path will be normalized. That means:\n     *  - all '..' and '.' segments are resolved. \n     *  - multiple, sequential occurences of '/' are replaced by a single instance of '/'.\n     *  - trailing separators are removed.\n     * \n     * @param uri The input URI.\n     * @param paths The paths to resolve against the path of URI.\n     * @returns A URI with the resolved path. All other properties of the URI (scheme, authority, query, fragments, ...) will be taken from the input URI.\n     */\n    export function resolvePath(uri: URI, ...paths: string[]): URI {\n        let path = uri.path; \n        let slashAdded = false;\n        if (path[0] !== slash) {\n            path = slash + path; // make the path abstract: for posixPath.resolve the first segments has to be absolute or cwd is used.\n            slashAdded = true;\n        }\n        let resolvedPath = posixPath.resolve(path, ...paths);\n        if (slashAdded && resolvedPath[0] === slash && !uri.authority) {\n            resolvedPath = resolvedPath.substring(1);\n        }\n        return uri.with({ path: resolvedPath });\n    }\n\n    /**\n     * Returns a URI where the path is the directory name of the input uri, similar to the Unix dirname command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The orignal URI is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The last segment of the URIs path.\n     */\n    export function dirname(uri: URI): URI {\n        if (uri.path.length === 0 || uri.path === slash) {\n            return uri;\n        }\n        let path = posixPath.dirname(uri.path);\n        if (path.length === 1 && path.charCodeAt(0) === CharCode.Period) {\n            path = '';\n        }\n        return uri.with({ path });\n    }\n\n    /**\n     * Returns the last segment of the path of a URI, similar to the Unix basename command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The empty string is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The base name of the URIs path.\n     */\n    export function basename(uri: URI): string {\n        return posixPath.basename(uri.path);\n    }\n\n    /**\n     * Returns the extension name of the path of a URI, similar to the Unix extname command. \n     * In the path, '/' is recognized as the directory separation character. Trailing directory separators are ignored.\n     * The empty string is returned if the URIs path is empty or does not contain any path segments.\n     * \n     * @param uri The input URI.\n     * @return The extension name of the URIs path.\n     */\n    export function extname(uri: URI): string {\n        return posixPath.extname(uri.path);\n    }\n}"], "names": ["assertPath", "path", "TypeError", "JSON", "stringify", "normalizeStringPosix", "allowAboveRoot", "code", "res", "lastSegmentLength", "lastSlash", "dots", "i", "length", "charCodeAt", "lastSlashIndex", "lastIndexOf", "slice", "posix", "resolve", "cwd", "<PERSON><PERSON><PERSON>", "resolvedAbsolute", "arguments", "undefined", "process", "normalize", "isAbsolute", "trailingSeparator", "join", "joined", "arg", "relative", "from", "to", "fromStart", "fromEnd", "fromLen", "toStart", "toLen", "lastCommonSep", "fromCode", "out", "_makeLong", "dirname", "hasRoot", "end", "matchedSlash", "basename", "ext", "start", "extIdx", "firstNonSlashEnd", "extname", "startDot", "startPart", "preDotState", "format", "pathObject", "sep", "dir", "root", "base", "name", "_format", "parse", "ret", "delimiter", "win32", "module", "exports", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "d", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "isWindows", "platform", "navigator", "userAgent", "indexOf", "_schemePattern", "_singleSlashStart", "_doubleSlashStart", "_validateUri", "_strict", "scheme", "Error", "authority", "query", "fragment", "test", "_empty", "_slash", "_regexp", "URI", "<PERSON><PERSON><PERSON>", "thing", "fsPath", "with", "toString", "schemeOrData", "this", "_schemeFix", "_referenceResolution", "uriToFsPath", "change", "<PERSON><PERSON>", "match", "exec", "percentDecode", "file", "replace", "idx", "substring", "components", "result", "skip<PERSON><PERSON><PERSON>", "_asFormatted", "toJSON", "revive", "data", "_formatted", "external", "_fsPath", "_sep", "_pathSepMarker", "$mid", "encodeTable", "encodeURIComponentFast", "uriComponent", "isPath", "isAuthority", "nativeEncodePos", "pos", "encodeURIComponent", "char<PERSON>t", "substr", "escaped", "encodeURIComponentMinimal", "uri", "keepDriveLetterCasing", "toLowerCase", "encoder", "userinfo", "String", "fromCharCode", "decodeURIComponentGraceful", "str", "decodeURIComponent", "_rEncodedAsHex", "posixPath", "slash", "Utils", "joinPath", "paths", "<PERSON><PERSON><PERSON>", "slashAdded"], "sourceRoot": ""}