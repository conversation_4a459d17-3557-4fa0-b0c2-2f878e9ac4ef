import React, { KeyboardEvent } from 'react';
import { TodoPage } from '../../../shared/pages/TodoPage';

interface TodoInputProps {
  todoPage: TodoPage;
}

export const TodoInput: React.FC<TodoInputProps> = ({ todoPage }) => {
  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      todoPage.addTodo();
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    todoPage.setNewTodoText(e.target.value);
  };

  return (
    <div className="todo-input-container">
      <input
        type="text"
        className="todo-input"
        placeholder="What needs to be done?"
        value={todoPage.getNewTodoText()}
        onChange={handleChange}
        onKeyPress={handleKeyPress}
      />
      <button
        className="add-todo-btn"
        onClick={() => todoPage.addTodo()}
        disabled={!todoPage.getNewTodoText().trim()}
      >
        Add Todo
      </button>
    </div>
  );
};
