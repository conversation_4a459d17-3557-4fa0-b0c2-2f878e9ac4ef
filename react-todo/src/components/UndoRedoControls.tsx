import React from 'react';
import { TodoPage } from '../../../shared/pages/TodoPage';

interface UndoRedoControlsProps {
  todoPage: TodoPage;
}

export const UndoRedoControls: React.FC<UndoRedoControlsProps> = ({ todoPage }) => {
  return (
    <div className="undo-redo-controls">
      <button
        className="undo-btn"
        onClick={() => todoPage.undo()}
        disabled={!todoPage.canUndo()}
        title="Undo last action"
      >
        ↶ Undo
      </button>
      <button
        className="redo-btn"
        onClick={() => todoPage.redo()}
        disabled={!todoPage.canRedo()}
        title="Redo last action"
      >
        ↷ Redo
      </button>
    </div>
  );
};
