import React from 'react';
import { TodoPage } from '../../../shared/pages/TodoPage';
import { TodoItem } from './TodoItem';

interface TodoListProps {
  todoPage: TodoPage;
}

export const TodoList: React.FC<TodoListProps> = ({ todoPage }) => {
  const todos = todoPage.getFilteredTodos();

  if (todos.length === 0) {
    const filter = todoPage.getCurrentFilter();
    const message = filter === 'all' 
      ? 'No todos yet. Add one above!'
      : filter === 'active'
      ? 'No active todos!'
      : 'No completed todos!';

    return (
      <div className="todo-list-empty">
        <p>{message}</p>
      </div>
    );
  }

  return (
    <div className="todo-list">
      {todos.map(todo => (
        <TodoItem
          key={todo.id.value}
          todo={todo}
          todoPage={todoPage}
        />
      ))}
    </div>
  );
};
