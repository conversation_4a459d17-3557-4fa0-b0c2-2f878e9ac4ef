import React, { useState } from 'react';
import { Todo } from '../../../shared/core/entities/Todo';
import { TodoPage } from '../../../shared/pages/TodoPage';

interface TodoItemProps {
  todo: Todo;
  todoPage: TodoPage;
}

export const TodoItem: React.FC<TodoItemProps> = ({ todo, todoPage }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(todo.text);

  const handleToggle = () => {
    todoPage.toggleTodo(todo.id);
  };

  const handleDelete = () => {
    todoPage.removeTodo(todo.id);
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditText(todo.text);
  };

  const handleSave = () => {
    if (editText.trim()) {
      todoPage.updateTodoText(todo.id, editText.trim());
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditText(todo.text);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  return (
    <div className={`todo-item ${todo.completed ? 'completed' : ''}`}>
      <div className="todo-item-content">
        <input
          type="checkbox"
          className="todo-checkbox"
          checked={todo.completed}
          onChange={handleToggle}
        />
        
        {isEditing ? (
          <input
            type="text"
            className="todo-edit-input"
            value={editText}
            onChange={(e) => setEditText(e.target.value)}
            onKeyPress={handleKeyPress}
            onBlur={handleSave}
            autoFocus
          />
        ) : (
          <span 
            className="todo-text"
            onDoubleClick={handleEdit}
          >
            {todo.text}
          </span>
        )}
      </div>

      <div className="todo-item-actions">
        {!isEditing && (
          <>
            <button
              className="edit-btn"
              onClick={handleEdit}
              title="Edit todo"
            >
              ✏️
            </button>
            <button
              className="delete-btn"
              onClick={handleDelete}
              title="Delete todo"
            >
              🗑️
            </button>
          </>
        )}
        
        {isEditing && (
          <>
            <button
              className="save-btn"
              onClick={handleSave}
              title="Save changes"
            >
              ✅
            </button>
            <button
              className="cancel-btn"
              onClick={handleCancel}
              title="Cancel editing"
            >
              ❌
            </button>
          </>
        )}
      </div>
    </div>
  );
};
