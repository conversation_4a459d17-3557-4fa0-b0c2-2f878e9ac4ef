.todo-app {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.todo-header {
  text-align: center;
  margin-bottom: 30px;
}

.todo-header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.architecture-note {
  color: #7f8c8d;
  font-style: italic;
  margin: 0;
}

.todo-main {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.todo-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.todo-input-container {
  flex: 1;
  display: flex;
  gap: 10px;
}

.todo-input {
  flex: 1;
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.todo-input:focus {
  outline: none;
  border-color: #3498db;
}

.add-todo-btn {
  padding: 12px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.add-todo-btn:hover:not(:disabled) {
  background: #2980b9;
}

.add-todo-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.undo-redo-controls {
  display: flex;
  gap: 8px;
}

.undo-btn, .redo-btn {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.undo-btn:hover:not(:disabled), .redo-btn:hover:not(:disabled) {
  border-color: #3498db;
  background: #f8f9fa;
}

.undo-btn:disabled, .redo-btn:disabled {
  color: #bdc3c7;
  cursor: not-allowed;
}

.todo-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.filter-buttons {
  display: flex;
  gap: 10px;
}

.filter-btn {
  padding: 8px 16px;
  border: 2px solid #e1e8ed;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.filter-btn:hover {
  border-color: #3498db;
}

.filter-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.clear-completed-btn {
  padding: 8px 16px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.clear-completed-btn:hover {
  background: #c0392b;
}

.todo-list {
  margin-bottom: 20px;
}

.todo-list-empty {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
  font-style: italic;
}

.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e1e8ed;
  transition: background-color 0.2s;
}

.todo-item:hover {
  background: #f8f9fa;
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 12px;
}

.todo-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.todo-text {
  flex: 1;
  font-size: 16px;
  cursor: pointer;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
}

.todo-edit-input {
  flex: 1;
  padding: 8px;
  border: 2px solid #3498db;
  border-radius: 4px;
  font-size: 16px;
}

.todo-item-actions {
  display: flex;
  gap: 8px;
}

.edit-btn, .delete-btn, .save-btn, .cancel-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: opacity 0.2s;
}

.edit-btn, .save-btn {
  background: #f39c12;
}

.delete-btn, .cancel-btn {
  background: #e74c3c;
}

.edit-btn:hover, .delete-btn:hover, .save-btn:hover, .cancel-btn:hover {
  opacity: 0.8;
}

.todo-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.stats-section {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stats-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.stat-label {
  font-weight: 500;
  color: #7f8c8d;
}

.stat-value {
  font-weight: bold;
  color: #2c3e50;
}

.todo-footer {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .todo-app {
    padding: 10px;
  }
  
  .todo-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .todo-filters {
    flex-direction: column;
    gap: 15px;
  }
  
  .todo-stats {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
