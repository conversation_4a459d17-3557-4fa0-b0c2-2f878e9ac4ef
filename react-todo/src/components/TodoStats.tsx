import React from 'react';
import { TodoPage } from '../../../shared/pages/TodoPage';

interface TodoStatsProps {
  todoPage: TodoPage;
}

export const TodoStats: React.FC<TodoStatsProps> = ({ todoPage }) => {
  const stats = todoPage.getStats();
  const eventStats = todoPage.getEventStoreStats();

  return (
    <div className="todo-stats">
      <div className="stats-section">
        <h3>Todo Statistics</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-label">Total:</span>
            <span className="stat-value">{stats.total}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Active:</span>
            <span className="stat-value">{stats.active}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Completed:</span>
            <span className="stat-value">{stats.completed}</span>
          </div>
        </div>
      </div>

      <div className="stats-section">
        <h3>Event Sourcing Stats</h3>
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-label">Events:</span>
            <span className="stat-value">{eventStats.totalEvents}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Version:</span>
            <span className="stat-value">{stats.version}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Event Types:</span>
            <span className="stat-value">{eventStats.eventTypes.length}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
