import React from 'react';
import { usePageInstance } from '../../../shared/adapters/react';
import { TodoPage } from '../../../shared/pages/TodoPage';
import { TodoList } from './TodoList';
import { TodoInput } from './TodoInput';
import { TodoFilters } from './TodoFilters';
import { TodoStats } from './TodoStats';
import { UndoRedoControls } from './UndoRedoControls';
import './TodoApp.css';

/**
 * React Todo Application demonstrating framework-agnostic architecture
 * Uses the shared TodoPage class through the React adapter
 */
export const TodoApp: React.FC = () => {
  // Use the framework-agnostic TodoPage through React adapter
  const todoPage = usePageInstance(TodoPage);

  return (
    <div className="todo-app">
      <header className="todo-header">
        <h1>React Todo App</h1>
        <p className="architecture-note">
          Framework-Agnostic Reactive Architecture Demo
        </p>
      </header>

      <main className="todo-main">
        <div className="todo-controls">
          <TodoInput todoPage={todoPage} />
          <UndoRedoControls todoPage={todoPage} />
        </div>

        <TodoFilters todoPage={todoPage} />
        <TodoList todoPage={todoPage} />
        <TodoStats todoPage={todoPage} />
      </main>

      <footer className="todo-footer">
        <p>
          This app demonstrates a framework-agnostic reactive architecture.
          The same business logic powers both React and Vue versions.
        </p>
      </footer>
    </div>
  );
};
