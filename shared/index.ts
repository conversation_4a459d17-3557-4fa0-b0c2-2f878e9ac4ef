/**
 * Framework-Agnostic Reactive Frontend Architecture
 * 
 * This module exports a complete framework-independent reactive architecture
 * that can be used with any UI framework (React, Vue, Angular, etc.)
 * 
 * Key Features:
 * - Framework-agnostic Page classes with explicit reactivity
 * - CQRS (Command-Query Responsibility Segregation)
 * - Event Sourcing with undo/redo capabilities
 * - Domain-Driven Design patterns
 * - Framework adapters for React and Vue
 */

// Core Architecture
export { Page } from './core/Page.js';

// Domain Layer
export { TodoId } from './core/value-objects/TodoId.js';
export { Todo } from './core/entities/Todo.js';
export { TodoAggregate } from './core/aggregates/TodoAggregate.js';

// Events & Event Sourcing
export {
  DomainEvent,
  TodoAddedEvent,
  TodoCompletedEvent,
  TodoUncompletedEvent,
  TodoRemovedEvent,
  TodoTextUpdatedEvent,
  AllCompletedTodosClearedEvent,
  TodoEventFactory
} from './core/events/TodoEvents.js';

// Infrastructure
export { EventStore } from './infrastructure/EventStore.js';

// Application Layer
export { TodoPage } from './pages/TodoPage.js';
export type { TodoFilter, TodoStats } from './pages/TodoPage.js';

// Framework Adapters
export {
  usePage as usePageReact,
  usePageInstance as usePageInstanceReact,
  withPage,
  PageProvider as ReactPageProvider,
  usePageContext as usePageContextReact
} from './adapters/react.js';

export {
  usePage as usePageVue,
  usePageInstance as usePageInstanceVue,
  useReactivePage,
  createPagePlugin,
  vPage,
  PageMixin
} from './adapters/vue.js';
