/**
 * Domain Events for Todo operations
 * Used for event sourcing and maintaining audit trail
 */

export class DomainEvent {
  constructor(type, aggregateId, data = {}, timestamp = new Date()) {
    this.type = type;
    this.aggregateId = aggregateId;
    this.data = data;
    this.timestamp = timestamp;
    this.id = crypto.randomUUID();
  }

  toJSON() {
    return {
      id: this.id,
      type: this.type,
      aggregateId: this.aggregateId,
      data: this.data,
      timestamp: this.timestamp.toISOString()
    };
  }

  static fromJSON(data) {
    const event = new DomainEvent(
      data.type,
      data.aggregateId,
      data.data,
      new Date(data.timestamp)
    );
    event.id = data.id;
    return event;
  }
}

export class TodoAddedEvent extends DomainEvent {
  constructor(aggregateId, todoId, text, timestamp) {
    super('TodoAdded', aggregateId, { todoId, text }, timestamp);
  }
}

export class TodoCompletedEvent extends DomainEvent {
  constructor(aggregateId, todoId, timestamp) {
    super('TodoCompleted', aggregateId, { todoId }, timestamp);
  }
}

export class TodoUncompletedEvent extends DomainEvent {
  constructor(aggregateId, todoId, timestamp) {
    super('TodoUncompleted', aggregateId, { todoId }, timestamp);
  }
}

export class TodoRemovedEvent extends DomainEvent {
  constructor(aggregateId, todoId, timestamp) {
    super('TodoRemoved', aggregateId, { todoId }, timestamp);
  }
}

export class TodoTextUpdatedEvent extends DomainEvent {
  constructor(aggregateId, todoId, newText, timestamp) {
    super('TodoTextUpdated', aggregateId, { todoId, newText }, timestamp);
  }
}

export class AllCompletedTodosClearedEvent extends DomainEvent {
  constructor(aggregateId, clearedTodoIds, timestamp) {
    super('AllCompletedTodosCleared', aggregateId, { clearedTodoIds }, timestamp);
  }
}

/**
 * Event factory for creating events from JSON
 */
export class TodoEventFactory {
  static fromJSON(data) {
    const baseEvent = DomainEvent.fromJSON(data);
    
    switch (data.type) {
      case 'TodoAdded':
        return Object.assign(new TodoAddedEvent(
          data.aggregateId,
          data.data.todoId,
          data.data.text,
          new Date(data.timestamp)
        ), { id: data.id });
        
      case 'TodoCompleted':
        return Object.assign(new TodoCompletedEvent(
          data.aggregateId,
          data.data.todoId,
          new Date(data.timestamp)
        ), { id: data.id });
        
      case 'TodoUncompleted':
        return Object.assign(new TodoUncompletedEvent(
          data.aggregateId,
          data.data.todoId,
          new Date(data.timestamp)
        ), { id: data.id });
        
      case 'TodoRemoved':
        return Object.assign(new TodoRemovedEvent(
          data.aggregateId,
          data.data.todoId,
          new Date(data.timestamp)
        ), { id: data.id });
        
      case 'TodoTextUpdated':
        return Object.assign(new TodoTextUpdatedEvent(
          data.aggregateId,
          data.data.todoId,
          data.data.newText,
          new Date(data.timestamp)
        ), { id: data.id });
        
      case 'AllCompletedTodosCleared':
        return Object.assign(new AllCompletedTodosClearedEvent(
          data.aggregateId,
          data.data.clearedTodoIds,
          new Date(data.timestamp)
        ), { id: data.id });
        
      default:
        return baseEvent;
    }
  }
}
