/**
 * Base Page class providing framework-agnostic reactive state management
 * Inspired by <PERSON><PERSON><PERSON>'s ChangeNotifier pattern
 */
export class Page {
  constructor() {
    this._listeners = new Set();
    this._disposed = false;
  }

  /**
   * Subscribe to state changes
   * @param {Function} listener - Callback function to be called on state changes
   * @returns {Function} Unsubscribe function
   */
  subscribe(listener) {
    if (this._disposed) {
      throw new Error('Cannot subscribe to disposed Page');
    }
    
    this._listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this._listeners.delete(listener);
    };
  }

  /**
   * Notify all listeners of state changes
   * Should be called after any state mutation
   */
  notifyListeners() {
    if (this._disposed) return;
    
    this._listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('Error in Page listener:', error);
      }
    });
  }

  /**
   * Dispose of the page and clean up listeners
   */
  dispose() {
    this._listeners.clear();
    this._disposed = true;
  }

  /**
   * Check if the page has been disposed
   */
  get isDisposed() {
    return this._disposed;
  }

  /**
   * Get the number of active listeners (useful for debugging)
   */
  get listenerCount() {
    return this._listeners.size;
  }
}
