import { TodoId } from '../value-objects/TodoId.js';

/**
 * Todo Entity
 * Represents a todo item with identity and behavior
 */
export class Todo {
  constructor(id, text, completed = false, createdAt = new Date()) {
    if (!(id instanceof TodoId)) {
      throw new Error('Todo id must be a TodoId instance');
    }
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      throw new Error('Todo text must be a non-empty string');
    }

    this._id = id;
    this._text = text.trim();
    this._completed = Boolean(completed);
    this._createdAt = new Date(createdAt);
    this._updatedAt = new Date();
  }

  get id() {
    return this._id;
  }

  get text() {
    return this._text;
  }

  get completed() {
    return this._completed;
  }

  get createdAt() {
    return new Date(this._createdAt);
  }

  get updatedAt() {
    return new Date(this._updatedAt);
  }

  /**
   * Create a new Todo with updated text
   */
  updateText(newText) {
    if (!newText || typeof newText !== 'string' || newText.trim().length === 0) {
      throw new Error('Todo text must be a non-empty string');
    }
    
    return new Todo(this._id, newText.trim(), this._completed, this._createdAt);
  }

  /**
   * Create a new Todo with toggled completion status
   */
  toggleCompleted() {
    return new Todo(this._id, this._text, !this._completed, this._createdAt);
  }

  /**
   * Create a new Todo marked as completed
   */
  markCompleted() {
    return new Todo(this._id, this._text, true, this._createdAt);
  }

  /**
   * Create a new Todo marked as incomplete
   */
  markIncomplete() {
    return new Todo(this._id, this._text, false, this._createdAt);
  }

  /**
   * Check equality with another Todo
   */
  equals(other) {
    return other instanceof Todo && this._id.equals(other._id);
  }

  /**
   * Convert to plain object for serialization
   */
  toJSON() {
    return {
      id: this._id.value,
      text: this._text,
      completed: this._completed,
      createdAt: this._createdAt.toISOString(),
      updatedAt: this._updatedAt.toISOString()
    };
  }

  /**
   * Create Todo from plain object
   */
  static fromJSON(data) {
    return new Todo(
      TodoId.fromString(data.id),
      data.text,
      data.completed,
      new Date(data.createdAt)
    );
  }
}
