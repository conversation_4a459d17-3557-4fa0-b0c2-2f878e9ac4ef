import { Todo } from '../entities/Todo.js';
import { TodoId } from '../value-objects/TodoId.js';
import {
  TodoAddedEvent,
  TodoCompletedEvent,
  TodoUncompletedEvent,
  TodoRemovedEvent,
  TodoTextUpdatedEvent,
  AllCompletedTodosClearedEvent
} from '../events/TodoEvents.js';

/**
 * TodoAggregate - Domain aggregate for managing todo collections
 * Implements event sourcing and domain logic
 */
export class TodoAggregate {
  constructor(id = crypto.randomUUID()) {
    this.id = id;
    this.todos = new Map();
    this.version = 0;
    this.uncommittedEvents = [];
  }

  /**
   * Add a new todo
   */
  addTodo(text) {
    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      throw new Error('Todo text must be a non-empty string');
    }

    const todoId = TodoId.generate();
    const todo = new Todo(todoId, text.trim());
    
    const event = new TodoAddedEvent(this.id, todoId.value, text.trim());
    this.applyEvent(event);
    this.uncommittedEvents.push(event);
    
    return todoId;
  }

  /**
   * Toggle todo completion status
   */
  toggleTodo(todoId) {
    const todoIdObj = typeof todoId === 'string' ? TodoId.fromString(todoId) : todoId;
    const todo = this.todos.get(todoIdObj.value);
    
    if (!todo) {
      throw new Error(`Todo with id ${todoIdObj.value} not found`);
    }

    const event = todo.completed 
      ? new TodoUncompletedEvent(this.id, todoIdObj.value)
      : new TodoCompletedEvent(this.id, todoIdObj.value);
    
    this.applyEvent(event);
    this.uncommittedEvents.push(event);
  }

  /**
   * Remove a todo
   */
  removeTodo(todoId) {
    const todoIdObj = typeof todoId === 'string' ? TodoId.fromString(todoId) : todoId;
    
    if (!this.todos.has(todoIdObj.value)) {
      throw new Error(`Todo with id ${todoIdObj.value} not found`);
    }

    const event = new TodoRemovedEvent(this.id, todoIdObj.value);
    this.applyEvent(event);
    this.uncommittedEvents.push(event);
  }

  /**
   * Update todo text
   */
  updateTodoText(todoId, newText) {
    if (!newText || typeof newText !== 'string' || newText.trim().length === 0) {
      throw new Error('Todo text must be a non-empty string');
    }

    const todoIdObj = typeof todoId === 'string' ? TodoId.fromString(todoId) : todoId;
    
    if (!this.todos.has(todoIdObj.value)) {
      throw new Error(`Todo with id ${todoIdObj.value} not found`);
    }

    const event = new TodoTextUpdatedEvent(this.id, todoIdObj.value, newText.trim());
    this.applyEvent(event);
    this.uncommittedEvents.push(event);
  }

  /**
   * Clear all completed todos
   */
  clearCompleted() {
    const completedTodoIds = Array.from(this.todos.values())
      .filter(todo => todo.completed)
      .map(todo => todo.id.value);

    if (completedTodoIds.length === 0) {
      return;
    }

    const event = new AllCompletedTodosClearedEvent(this.id, completedTodoIds);
    this.applyEvent(event);
    this.uncommittedEvents.push(event);
  }

  /**
   * Apply an event to update the aggregate state
   */
  applyEvent(event) {
    switch (event.type) {
      case 'TodoAdded':
        this.todos.set(event.data.todoId, new Todo(
          TodoId.fromString(event.data.todoId),
          event.data.text
        ));
        break;

      case 'TodoCompleted':
        const todoToComplete = this.todos.get(event.data.todoId);
        if (todoToComplete) {
          this.todos.set(event.data.todoId, todoToComplete.markCompleted());
        }
        break;

      case 'TodoUncompleted':
        const todoToUncomplete = this.todos.get(event.data.todoId);
        if (todoToUncomplete) {
          this.todos.set(event.data.todoId, todoToUncomplete.markIncomplete());
        }
        break;

      case 'TodoRemoved':
        this.todos.delete(event.data.todoId);
        break;

      case 'TodoTextUpdated':
        const todoToUpdate = this.todos.get(event.data.todoId);
        if (todoToUpdate) {
          this.todos.set(event.data.todoId, todoToUpdate.updateText(event.data.newText));
        }
        break;

      case 'AllCompletedTodosCleared':
        event.data.clearedTodoIds.forEach(todoId => {
          this.todos.delete(todoId);
        });
        break;
    }

    this.version++;
  }

  /**
   * Replay events to rebuild state
   */
  replayEvents(events) {
    this.todos.clear();
    this.version = 0;
    this.uncommittedEvents = [];

    events.forEach(event => this.applyEvent(event));
  }

  /**
   * Get uncommitted events and mark them as committed
   */
  getUncommittedEvents() {
    const events = [...this.uncommittedEvents];
    this.uncommittedEvents = [];
    return events;
  }

  /**
   * Get all todos as an array
   */
  getAllTodos() {
    return Array.from(this.todos.values());
  }

  /**
   * Get todo by ID
   */
  getTodo(todoId) {
    const todoIdObj = typeof todoId === 'string' ? TodoId.fromString(todoId) : todoId;
    return this.todos.get(todoIdObj.value) || null;
  }

  /**
   * Get aggregate statistics
   */
  getStats() {
    const todos = this.getAllTodos();
    return {
      total: todos.length,
      completed: todos.filter(t => t.completed).length,
      active: todos.filter(t => !t.completed).length,
      version: this.version
    };
  }
}
