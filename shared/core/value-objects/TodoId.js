/**
 * TodoId Value Object
 * Represents a unique identifier for a todo item
 */
export class TodoId {
  constructor(value) {
    if (!value || typeof value !== 'string') {
      throw new Error('TodoId must be a non-empty string');
    }
    this._value = value;
  }

  get value() {
    return this._value;
  }

  equals(other) {
    return other instanceof TodoId && this._value === other._value;
  }

  toString() {
    return this._value;
  }

  static generate() {
    return new TodoId(crypto.randomUUID());
  }

  static fromString(value) {
    return new TodoId(value);
  }
}
