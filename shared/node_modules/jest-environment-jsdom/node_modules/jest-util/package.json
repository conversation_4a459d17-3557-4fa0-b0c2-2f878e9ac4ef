{"name": "jest-util", "version": "30.0.0-beta.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-util"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/types": "30.0.0-beta.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^4.0.0", "graceful-fs": "^4.2.9", "picomatch": "^4.0.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/picomatch": "^4.0.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017"}