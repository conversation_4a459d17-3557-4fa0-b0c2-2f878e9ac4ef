{"name": "@jest/types", "version": "30.0.0-beta.3", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-types"}, "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/pattern": "30.0.0-beta.3", "@jest/schemas": "30.0.0-beta.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017"}