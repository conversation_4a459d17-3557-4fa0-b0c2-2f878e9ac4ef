import { TodoPage } from '../pages/TodoPage.js';
import { TodoId } from '../core/value-objects/TodoId.js';

/**
 * Unit tests for TodoPage demonstrating framework-agnostic testability
 */

describe('TodoPage', () => {
  let todoPage;

  beforeEach(() => {
    todoPage = new TodoPage();
    todoPage.clear(); // Start with clean state
  });

  afterEach(() => {
    if (todoPage && !todoPage.isDisposed) {
      todoPage.dispose();
    }
  });

  describe('Page lifecycle', () => {
    test('should initialize with empty state', () => {
      expect(todoPage.getAllTodos()).toHaveLength(0);
      expect(todoPage.getCurrentFilter()).toBe('all');
      expect(todoPage.getNewTodoText()).toBe('');
      expect(todoPage.getStats().total).toBe(0);
    });

    test('should support subscription and notification', () => {
      const listener = jest.fn();
      const unsubscribe = todoPage.subscribe(listener);

      todoPage.addTodo('Test todo');
      expect(listener).toHaveBeenCalledTimes(1);

      unsubscribe();
      todoPage.addTodo('Another todo');
      expect(listener).toHaveBeenCalledTimes(1); // Should not be called after unsubscribe
    });

    test('should dispose properly', () => {
      const listener = jest.fn();
      todoPage.subscribe(listener);

      todoPage.dispose();
      expect(todoPage.isDisposed).toBe(true);
      expect(todoPage.listenerCount).toBe(0);
    });
  });

  describe('CQRS Commands', () => {
    test('should add todo', () => {
      const todoId = todoPage.addTodo('Buy groceries');
      
      expect(todoId).toBeInstanceOf(TodoId);
      expect(todoPage.getAllTodos()).toHaveLength(1);
      expect(todoPage.getAllTodos()[0].text).toBe('Buy groceries');
      expect(todoPage.getAllTodos()[0].completed).toBe(false);
    });

    test('should not add empty todo', () => {
      const result = todoPage.addTodo('');
      expect(result).toBeUndefined();
      expect(todoPage.getAllTodos()).toHaveLength(0);

      const result2 = todoPage.addTodo('   ');
      expect(result2).toBeUndefined();
      expect(todoPage.getAllTodos()).toHaveLength(0);
    });

    test('should toggle todo completion', () => {
      const todoId = todoPage.addTodo('Test todo');
      const todo = todoPage.getTodo(todoId);
      
      expect(todo.completed).toBe(false);
      
      todoPage.toggleTodo(todoId);
      const updatedTodo = todoPage.getTodo(todoId);
      expect(updatedTodo.completed).toBe(true);
      
      todoPage.toggleTodo(todoId);
      const toggledBackTodo = todoPage.getTodo(todoId);
      expect(toggledBackTodo.completed).toBe(false);
    });

    test('should remove todo', () => {
      const todoId = todoPage.addTodo('Test todo');
      expect(todoPage.getAllTodos()).toHaveLength(1);
      
      todoPage.removeTodo(todoId);
      expect(todoPage.getAllTodos()).toHaveLength(0);
      expect(todoPage.getTodo(todoId)).toBeNull();
    });

    test('should update todo text', () => {
      const todoId = todoPage.addTodo('Original text');
      
      todoPage.updateTodoText(todoId, 'Updated text');
      const updatedTodo = todoPage.getTodo(todoId);
      expect(updatedTodo.text).toBe('Updated text');
    });

    test('should clear completed todos', () => {
      const todoId1 = todoPage.addTodo('Todo 1');
      const todoId2 = todoPage.addTodo('Todo 2');
      const todoId3 = todoPage.addTodo('Todo 3');
      
      todoPage.toggleTodo(todoId1);
      todoPage.toggleTodo(todoId3);
      
      expect(todoPage.getAllTodos()).toHaveLength(3);
      expect(todoPage.getStats().completed).toBe(2);
      
      todoPage.clearCompleted();
      
      expect(todoPage.getAllTodos()).toHaveLength(1);
      expect(todoPage.getTodo(todoId2).text).toBe('Todo 2');
      expect(todoPage.getTodo(todoId1)).toBeNull();
      expect(todoPage.getTodo(todoId3)).toBeNull();
    });

    test('should set filter', () => {
      todoPage.setFilter('active');
      expect(todoPage.getCurrentFilter()).toBe('active');
      
      todoPage.setFilter('completed');
      expect(todoPage.getCurrentFilter()).toBe('completed');
      
      expect(() => todoPage.setFilter('invalid')).toThrow();
    });

    test('should set new todo text', () => {
      todoPage.setNewTodoText('New todo text');
      expect(todoPage.getNewTodoText()).toBe('New todo text');
    });
  });

  describe('CQRS Queries', () => {
    beforeEach(() => {
      todoPage.addTodo('Active todo 1');
      const todoId2 = todoPage.addTodo('Completed todo');
      todoPage.addTodo('Active todo 2');
      
      todoPage.toggleTodo(todoId2);
    });

    test('should get all todos', () => {
      const todos = todoPage.getAllTodos();
      expect(todos).toHaveLength(3);
    });

    test('should filter todos correctly', () => {
      todoPage.setFilter('all');
      expect(todoPage.getFilteredTodos()).toHaveLength(3);
      
      todoPage.setFilter('active');
      const activeTodos = todoPage.getFilteredTodos();
      expect(activeTodos).toHaveLength(2);
      expect(activeTodos.every(todo => !todo.completed)).toBe(true);
      
      todoPage.setFilter('completed');
      const completedTodos = todoPage.getFilteredTodos();
      expect(completedTodos).toHaveLength(1);
      expect(completedTodos.every(todo => todo.completed)).toBe(true);
    });

    test('should get correct stats', () => {
      const stats = todoPage.getStats();
      expect(stats.total).toBe(3);
      expect(stats.active).toBe(2);
      expect(stats.completed).toBe(1);
    });

    test('should detect completed todos', () => {
      expect(todoPage.hasCompletedTodos()).toBe(true);
      
      todoPage.clearCompleted();
      expect(todoPage.hasCompletedTodos()).toBe(false);
    });
  });

  describe('Event Sourcing & Undo/Redo', () => {
    test('should support undo/redo', () => {
      expect(todoPage.canUndo()).toBe(false);
      expect(todoPage.canRedo()).toBe(false);
      
      const todoId1 = todoPage.addTodo('Todo 1');
      expect(todoPage.canUndo()).toBe(true);
      expect(todoPage.canRedo()).toBe(false);
      
      const todoId2 = todoPage.addTodo('Todo 2');
      expect(todoPage.getAllTodos()).toHaveLength(2);
      
      // Undo last addition
      todoPage.undo();
      expect(todoPage.getAllTodos()).toHaveLength(1);
      expect(todoPage.canRedo()).toBe(true);
      
      // Redo
      todoPage.redo();
      expect(todoPage.getAllTodos()).toHaveLength(2);
      expect(todoPage.canRedo()).toBe(false);
      
      // Undo twice
      todoPage.undo();
      todoPage.undo();
      expect(todoPage.getAllTodos()).toHaveLength(0);
      expect(todoPage.canUndo()).toBe(false);
    });

    test('should maintain event store', () => {
      todoPage.addTodo('Test todo');
      const stats = todoPage.getEventStoreStats();
      
      expect(stats.totalEvents).toBeGreaterThan(0);
      expect(stats.eventTypes).toContain('TodoAdded');
    });
  });

  describe('Error handling', () => {
    test('should handle invalid operations gracefully', () => {
      expect(() => todoPage.toggleTodo('nonexistent')).toThrow();
      expect(() => todoPage.removeTodo('nonexistent')).toThrow();
      expect(() => todoPage.updateTodoText('nonexistent', 'text')).toThrow();
    });
  });

  describe('Reactivity', () => {
    test('should notify listeners on state changes', () => {
      const listener = jest.fn();
      todoPage.subscribe(listener);
      
      todoPage.addTodo('Test');
      expect(listener).toHaveBeenCalledTimes(1);
      
      todoPage.setFilter('active');
      expect(listener).toHaveBeenCalledTimes(2);
      
      todoPage.setNewTodoText('New text');
      expect(listener).toHaveBeenCalledTimes(3);
    });
  });
});
