import { Page } from '../core/Page.js';
import { TodoAggregate } from '../core/aggregates/TodoAggregate.js';
import { EventStore } from '../infrastructure/EventStore.js';

/**
 * TodoPage - Framework-agnostic page class implementing CQRS pattern
 * Commands: Mutate state through the aggregate
 * Queries: Provide projections for the UI
 */
export class TodoPage extends Page {
  constructor() {
    super();

    // Infrastructure
    this.eventStore = new EventStore('todoApp');
    this.aggregate = new TodoAggregate();

    // State projections for UI
    this.filter = 'all'; // 'all', 'active', 'completed'
    this.newTodoText = '';

    // Event sourcing: replay events to rebuild state
    this.replayEvents();

    // Undo/Redo support
    this.eventHistory = [];
    this.currentHistoryIndex = -1;
    this.maxHistorySize = 50;
  }

  // ============ COMMANDS (State Mutations) ============

  /**
   * Command: Add a new todo
   */
  addTodo(text = this.newTodoText) {
    if (!text || text.trim().length === 0) {
      return;
    }

    try {
      const todoId = this.aggregate.addTodo(text.trim());
      this.commitEvents();
      this.newTodoText = '';
      this.notifyListeners();
      return todoId;
    } catch (error) {
      console.error('Failed to add todo:', error);
      throw error;
    }
  }

  /**
   * Command: Toggle todo completion
   */
  toggleTodo(todoId) {
    try {
      this.aggregate.toggleTodo(todoId);
      this.commitEvents();
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to toggle todo:', error);
      throw error;
    }
  }

  /**
   * Command: Remove a todo
   */
  removeTodo(todoId) {
    try {
      this.aggregate.removeTodo(todoId);
      this.commitEvents();
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to remove todo:', error);
      throw error;
    }
  }

  /**
   * Command: Update todo text
   */
  updateTodoText(todoId, newText) {
    try {
      this.aggregate.updateTodoText(todoId, newText);
      this.commitEvents();
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to update todo text:', error);
      throw error;
    }
  }

  /**
   * Command: Clear all completed todos
   */
  clearCompleted() {
    try {
      this.aggregate.clearCompleted();
      this.commitEvents();
      this.notifyListeners();
    } catch (error) {
      console.error('Failed to clear completed todos:', error);
      throw error;
    }
  }

  /**
   * Command: Set filter
   */
  setFilter(filter) {
    if (!['all', 'active', 'completed'].includes(filter)) {
      throw new Error('Invalid filter. Must be "all", "active", or "completed"');
    }

    this.filter = filter;
    this.notifyListeners();
  }

  /**
   * Command: Set new todo text
   */
  setNewTodoText(text) {
    this.newTodoText = text;
    this.notifyListeners();
  }

  /**
   * Command: Undo last action
   */
  undo() {
    if (!this.canUndo()) {
      return false;
    }

    this.currentHistoryIndex--;
    this.replayToHistoryIndex();
    this.notifyListeners();
    return true;
  }

  /**
   * Command: Redo last undone action
   */
  redo() {
    if (!this.canRedo()) {
      return false;
    }

    this.currentHistoryIndex++;
    this.replayToHistoryIndex();
    this.notifyListeners();
    return true;
  }

  // ============ QUERIES (State Projections) ============

  /**
   * Query: Get all todos
   */
  getAllTodos() {
    return this.aggregate.getAllTodos();
  }

  /**
   * Query: Get filtered todos based on current filter
   */
  getFilteredTodos() {
    const todos = this.getAllTodos();

    switch (this.filter) {
      case 'active':
        return todos.filter(todo => !todo.completed);
      case 'completed':
        return todos.filter(todo => todo.completed);
      default:
        return todos;
    }
  }

  /**
   * Query: Get todo by ID
   */
  getTodo(todoId) {
    return this.aggregate.getTodo(todoId);
  }

  /**
   * Query: Get current filter
   */
  getCurrentFilter() {
    return this.filter;
  }

  /**
   * Query: Get new todo text
   */
  getNewTodoText() {
    return this.newTodoText;
  }

  /**
   * Query: Get todo statistics
   */
  getStats() {
    return this.aggregate.getStats();
  }

  /**
   * Query: Check if there are any completed todos
   */
  hasCompletedTodos() {
    return this.getAllTodos().some(todo => todo.completed);
  }

  /**
   * Query: Check if undo is possible
   */
  canUndo() {
    return this.currentHistoryIndex > 0;
  }

  /**
   * Query: Check if redo is possible
   */
  canRedo() {
    return this.currentHistoryIndex < this.eventHistory.length - 1;
  }

  /**
   * Query: Get event store statistics
   */
  getEventStoreStats() {
    return this.eventStore.getStats();
  }

  // ============ PRIVATE METHODS ============

  /**
   * Commit uncommitted events to the event store
   */
  commitEvents() {
    const events = this.aggregate.getUncommittedEvents();

    if (events.length > 0) {
      // Store events
      this.eventStore.appendBatch(events);

      // Update history for undo/redo
      this.addToHistory(events);
    }
  }

  /**
   * Replay all events to rebuild current state
   */
  replayEvents() {
    const events = this.eventStore.getEventsForAggregate(this.aggregate.id);
    this.aggregate.replayEvents(events);

    // Initialize history
    this.eventHistory = [[]]; // Start with empty state
    let currentEvents = [];

    events.forEach(event => {
      currentEvents.push(event);
      // Group events by timestamp (same operation)
      const nextEvent = events[events.indexOf(event) + 1];
      if (!nextEvent || nextEvent.timestamp.getTime() !== event.timestamp.getTime()) {
        this.eventHistory.push([...currentEvents]);
        currentEvents = [];
      }
    });

    this.currentHistoryIndex = this.eventHistory.length - 1;
  }

  /**
   * Add events to history for undo/redo
   */
  addToHistory(events) {
    // Remove any redo history if we're not at the end
    if (this.currentHistoryIndex < this.eventHistory.length - 1) {
      this.eventHistory = this.eventHistory.slice(0, this.currentHistoryIndex + 1);
    }

    // Add new events
    this.eventHistory.push([...events]);
    this.currentHistoryIndex = this.eventHistory.length - 1;

    // Limit history size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
      this.currentHistoryIndex = this.eventHistory.length - 1;
    }
  }

  /**
   * Replay events up to a specific history index
   */
  replayToHistoryIndex() {
    const eventsToReplay = [];

    for (let i = 1; i <= this.currentHistoryIndex; i++) {
      eventsToReplay.push(...this.eventHistory[i]);
    }

    this.aggregate.replayEvents(eventsToReplay);
  }

  /**
   * Clear all data (useful for testing)
   */
  clear() {
    this.eventStore.clear();
    this.aggregate = new TodoAggregate();
    this.filter = 'all';
    this.newTodoText = '';
    this.eventHistory = [[]];
    this.currentHistoryIndex = 0;
    this.notifyListeners();
  }
}
