{"name": "@ui-arch/shared", "version": "1.0.0", "description": "Framework-agnostic reactive frontend architecture", "type": "module", "main": "index.ts", "scripts": {"test": "jest", "test:watch": "jest --watch", "build": "tsc", "dev": "tsc --watch"}, "keywords": ["framework-agnostic", "reactive", "architecture", "cqrs", "event-sourcing", "ddd"], "author": "UI Architecture Demo", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"]}}