import { reactive, watchEffect, onUnmounted, ref } from 'vue';

/**
 * Vue adapter for framework-agnostic Page classes
 * Composable that connects Page instances to Vue's reactivity system
 */
export function usePage(pageInstance) {
  // Create a reactive wrapper that will trigger updates
  const reactiveState = reactive({
    _updateTrigger: 0
  });

  let unsubscribe = null;

  // Subscribe to page changes
  const subscribe = () => {
    unsubscribe = pageInstance.subscribe(() => {
      // Trigger reactivity by updating the trigger
      reactiveState._updateTrigger++;
    });
  };

  // Initial subscription
  subscribe();

  // Cleanup on unmount
  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe();
    }
  });

  // Create a proxy that triggers reactivity when accessed
  return new Proxy(pageInstance, {
    get(target, prop) {
      // Access the reactive trigger to establish dependency
      reactiveState._updateTrigger;
      
      const value = target[prop];
      
      // Bind methods to maintain correct 'this' context
      if (typeof value === 'function') {
        return value.bind(target);
      }
      
      return value;
    }
  });
}

/**
 * Vue composable for creating and managing a page instance
 */
export function usePageInstance(PageClass, ...args) {
  const pageInstance = new PageClass(...args);
  const page = usePage(pageInstance);
  
  onUnmounted(() => {
    if (!pageInstance.isDisposed) {
      pageInstance.dispose();
    }
  });
  
  return page;
}

/**
 * Vue plugin for global page management
 */
export function createPagePlugin() {
  return {
    install(app) {
      app.config.globalProperties.$usePage = usePage;
      app.config.globalProperties.$usePageInstance = usePageInstance;
      
      app.provide('usePage', usePage);
      app.provide('usePageInstance', usePageInstance);
    }
  };
}

/**
 * Vue composable for reactive page state
 * Alternative approach using Vue's reactive system more directly
 */
export function useReactivePage(pageInstance) {
  const state = reactive({
    page: pageInstance,
    lastUpdate: Date.now()
  });

  const unsubscribe = pageInstance.subscribe(() => {
    state.lastUpdate = Date.now();
  });

  onUnmounted(() => {
    unsubscribe();
  });

  // Return reactive proxy
  return new Proxy(state.page, {
    get(target, prop) {
      // Access lastUpdate to establish reactivity dependency
      state.lastUpdate;
      
      const value = target[prop];
      
      if (typeof value === 'function') {
        return value.bind(target);
      }
      
      return value;
    }
  });
}

/**
 * Vue directive for automatic page subscription
 */
export const vPage = {
  mounted(el, binding) {
    const page = binding.value;
    if (!page || typeof page.subscribe !== 'function') {
      console.warn('v-page directive requires a Page instance');
      return;
    }

    const unsubscribe = page.subscribe(() => {
      // Trigger a custom event that components can listen to
      el.dispatchEvent(new CustomEvent('page-updated', {
        detail: { page }
      }));
    });

    // Store unsubscribe function for cleanup
    el._pageUnsubscribe = unsubscribe;
  },

  unmounted(el) {
    if (el._pageUnsubscribe) {
      el._pageUnsubscribe();
      delete el._pageUnsubscribe;
    }
  }
};

/**
 * Vue mixin for page integration (for Options API)
 */
export const PageMixin = {
  data() {
    return {
      _pageUpdateTrigger: 0
    };
  },

  methods: {
    usePage(pageInstance) {
      if (this._pageUnsubscribe) {
        this._pageUnsubscribe();
      }

      this._pageUnsubscribe = pageInstance.subscribe(() => {
        this._pageUpdateTrigger++;
      });

      return pageInstance;
    }
  },

  beforeUnmount() {
    if (this._pageUnsubscribe) {
      this._pageUnsubscribe();
    }
  }
};
