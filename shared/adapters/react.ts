import { useState, useEffect, useRef, createContext, useContext, ReactNode } from 'react';
import { Page } from '../core/Page.js';

/**
 * React adapter for framework-agnostic Page classes
 * Custom hook that connects Page instances to React's reactivity system
 */
export function usePage<T extends Page>(pageInstance: T): T {
  // Force re-render when page state changes
  const [, forceUpdate] = useState({});
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // Subscribe to page changes
    const unsubscribe = pageInstance.subscribe(() => {
      forceUpdate({});
    });

    unsubscribeRef.current = unsubscribe;

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [pageInstance]);

  // Cleanup on page instance change
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [pageInstance]);

  return pageInstance;
}

/**
 * Higher-order component that provides a page instance to its children
 */
export function withPage<T extends Page, P = {}>(
  PageClass: new (props?: P) => T
) {
  return function PageProvider({
    children,
    ...props
  }: {
    children: (page: T) => ReactNode;
  } & P) {
    const pageInstanceRef = useRef<T | null>(null);

    if (!pageInstanceRef.current) {
      pageInstanceRef.current = new PageClass(props as P);
    }

    const page = usePage(pageInstanceRef.current);

    useEffect(() => {
      return () => {
        if (pageInstanceRef.current && !pageInstanceRef.current.isDisposed) {
          pageInstanceRef.current.dispose();
        }
      };
    }, []);

    return children(page);
  };
}

/**
 * React hook for creating and managing a page instance
 */
export function usePageInstance<T extends Page, Args extends any[]>(
  PageClass: new (...args: Args) => T,
  ...args: Args
): T {
  const pageRef = useRef<T | null>(null);

  if (!pageRef.current) {
    pageRef.current = new PageClass(...args);
  }

  const page = usePage(pageRef.current);

  useEffect(() => {
    return () => {
      if (pageRef.current && !pageRef.current.isDisposed) {
        pageRef.current.dispose();
      }
    };
  }, []);

  return page;
}

/**
 * React context for sharing page instances across components
 */
export const PageContext = createContext<Page | null>(null);

interface PageProviderProps<T extends Page> {
  page: T;
  children: ReactNode;
}

export function PageProvider<T extends Page>({ page, children }: PageProviderProps<T>) {
  const managedPage = usePage(page);

  return (
    <PageContext.Provider value={managedPage}>
      {children}
    </PageContext.Provider>
  );
}

export function usePageContext<T extends Page>(): T {
  const page = useContext(PageContext);
  if (!page) {
    throw new Error('usePageContext must be used within a PageProvider');
  }
  return page as T;
}
