import { useState, useEffect, useRef } from 'react';

/**
 * React adapter for framework-agnostic Page classes
 * Custom hook that connects Page instances to React's reactivity system
 */
export function usePage(pageInstance) {
  // Force re-render when page state changes
  const [, forceUpdate] = useState({});
  const unsubscribeRef = useRef(null);

  useEffect(() => {
    // Subscribe to page changes
    const unsubscribe = pageInstance.subscribe(() => {
      forceUpdate({});
    });
    
    unsubscribeRef.current = unsubscribe;

    // Cleanup subscription on unmount
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [pageInstance]);

  // Cleanup on page instance change
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [pageInstance]);

  return pageInstance;
}

/**
 * Higher-order component that provides a page instance to its children
 */
export function withPage(PageClass) {
  return function PageProvider({ children, ...props }) {
    const pageInstanceRef = useRef(null);
    
    if (!pageInstanceRef.current) {
      pageInstanceRef.current = new PageClass(props);
    }
    
    const page = usePage(pageInstanceRef.current);
    
    useEffect(() => {
      return () => {
        if (pageInstanceRef.current && !pageInstanceRef.current.isDisposed) {
          pageInstanceRef.current.dispose();
        }
      };
    }, []);

    return children(page);
  };
}

/**
 * React hook for creating and managing a page instance
 */
export function usePageInstance(PageClass, ...args) {
  const pageRef = useRef(null);
  
  if (!pageRef.current) {
    pageRef.current = new PageClass(...args);
  }
  
  const page = usePage(pageRef.current);
  
  useEffect(() => {
    return () => {
      if (pageRef.current && !pageRef.current.isDisposed) {
        pageRef.current.dispose();
      }
    };
  }, []);
  
  return page;
}

/**
 * React context for sharing page instances across components
 */
import { createContext, useContext } from 'react';

export const PageContext = createContext(null);

export function PageProvider({ page, children }) {
  const managedPage = usePage(page);
  
  return (
    <PageContext.Provider value={managedPage}>
      {children}
    </PageContext.Provider>
  );
}

export function usePageContext() {
  const page = useContext(PageContext);
  if (!page) {
    throw new Error('usePageContext must be used within a PageProvider');
  }
  return page;
}
