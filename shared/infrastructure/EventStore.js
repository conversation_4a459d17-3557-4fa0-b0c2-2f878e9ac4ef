import { TodoEventFactory } from '../core/events/TodoEvents.js';

/**
 * In-memory Event Store implementation
 * Supports event sourcing with persistence to localStorage
 */
export class EventStore {
  constructor(storageKey = 'eventStore') {
    this.storageKey = storageKey;
    this.events = [];
    this.snapshots = new Map();
    this.loadFromStorage();
  }

  /**
   * Append an event to the store
   */
  append(event) {
    this.events.push(event);
    this.saveToStorage();
    return event;
  }

  /**
   * Append multiple events atomically
   */
  appendBatch(events) {
    this.events.push(...events);
    this.saveToStorage();
    return events;
  }

  /**
   * Get all events for a specific aggregate
   */
  getEventsForAggregate(aggregateId, fromVersion = 0) {
    return this.events
      .filter(event => event.aggregateId === aggregateId)
      .slice(fromVersion);
  }

  /**
   * Get all events in chronological order
   */
  getAllEvents() {
    return [...this.events];
  }

  /**
   * Get events after a specific timestamp
   */
  getEventsAfter(timestamp) {
    return this.events.filter(event => event.timestamp > timestamp);
  }

  /**
   * Get events before a specific timestamp
   */
  getEventsBefore(timestamp) {
    return this.events.filter(event => event.timestamp <= timestamp);
  }

  /**
   * Clear all events (useful for testing)
   */
  clear() {
    this.events = [];
    this.snapshots.clear();
    this.saveToStorage();
  }

  /**
   * Get the current version (number of events) for an aggregate
   */
  getAggregateVersion(aggregateId) {
    return this.events.filter(event => event.aggregateId === aggregateId).length;
  }

  /**
   * Save a snapshot of aggregate state at a specific version
   */
  saveSnapshot(aggregateId, version, state) {
    this.snapshots.set(`${aggregateId}:${version}`, {
      aggregateId,
      version,
      state,
      timestamp: new Date()
    });
  }

  /**
   * Get the latest snapshot for an aggregate
   */
  getLatestSnapshot(aggregateId) {
    const snapshots = Array.from(this.snapshots.values())
      .filter(snapshot => snapshot.aggregateId === aggregateId)
      .sort((a, b) => b.version - a.version);
    
    return snapshots[0] || null;
  }

  /**
   * Persist events to localStorage
   */
  saveToStorage() {
    try {
      const data = {
        events: this.events.map(event => event.toJSON()),
        snapshots: Array.from(this.snapshots.entries())
      };
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save events to localStorage:', error);
    }
  }

  /**
   * Load events from localStorage
   */
  loadFromStorage() {
    try {
      const data = localStorage.getItem(this.storageKey);
      if (data) {
        const parsed = JSON.parse(data);
        this.events = parsed.events.map(eventData => TodoEventFactory.fromJSON(eventData));
        this.snapshots = new Map(parsed.snapshots || []);
      }
    } catch (error) {
      console.warn('Failed to load events from localStorage:', error);
      this.events = [];
      this.snapshots = new Map();
    }
  }

  /**
   * Get statistics about the event store
   */
  getStats() {
    const aggregates = new Set(this.events.map(e => e.aggregateId));
    const eventTypes = new Set(this.events.map(e => e.type));
    
    return {
      totalEvents: this.events.length,
      aggregateCount: aggregates.size,
      eventTypes: Array.from(eventTypes),
      snapshotCount: this.snapshots.size,
      oldestEvent: this.events[0]?.timestamp,
      newestEvent: this.events[this.events.length - 1]?.timestamp
    };
  }
}
